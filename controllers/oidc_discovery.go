// Copyright 2021 The Casdoor Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package controllers

import (
	"strings"

	"github.com/casdoor/casdoor/object"
)

// GetOidcDiscovery
// @Title GetOidcDiscovery
// @Tag OIDC API
// @Description Get Oidc Discovery
// @Success 200 {object} object.OidcDiscovery
// @router /.well-known/openid-configuration [get]
func (c *RootController) GetOidcDiscovery() {
	host := c.Ctx.Request.Host
	c.Data["json"] = object.GetOidcDiscovery(host)
	c.ServeJ<PERSON>N()
}

// GetJwks
// @Title GetJwks
// @Tag OIDC API
// @Success 200 {object} jose.JSONWebKey
// @router /.well-known/jwks [get]
func (c *RootController) GetJwks() {
	jwks, err := object.GetJsonWebKeySet()
	if err != nil {
		c.ResponseError(err.Error())
		return
	}
	c.Data["json"] = jwks
	c.ServeJSON()
}

// GetWebFinger
// @Title GetWebFinger
// @Tag OIDC API
// @Param resource query string true "resource"
// @Success 200 {object} object.WebFinger
// @router /.well-known/webfinger [get]
func (c *RootController) GetWebFinger() {
	resource := c.Input().Get("resource")
	rels := []string{}
	host := c.Ctx.Request.Host

	for key, value := range c.Input() {
		if strings.HasPrefix(key, "rel") {
			rels = append(rels, value...)
		}
	}

	webfinger, err := object.GetWebFinger(resource, rels, host)
	if err != nil {
		c.ResponseError(err.Error())
		return
	}

	c.Data["json"] = webfinger
	c.Ctx.Output.ContentType("application/jrd+json")
	c.ServeJSON()
}
