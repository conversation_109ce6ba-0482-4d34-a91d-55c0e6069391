// Copyright 2023 The Casdoor Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package conf

import (
	"encoding/json"

	"github.com/beego/beego"
)

type Quota struct {
	Organization int `json:"organization"`
	User         int `json:"user"`
	Application  int `json:"application"`
	Provider     int `json:"provider"`
}

var quota = &Quota{-1, -1, -1, -1}

func init() {
	initQuota()
}

func initQuota() {
	res := beego.AppConfig.String("quota")
	if res != "" {
		err := json.Unmarshal([]byte(res), quota)
		if err != nil {
			panic(err)
		}
	}
}

func GetConfigQuota() *Quota {
	return quota
}
