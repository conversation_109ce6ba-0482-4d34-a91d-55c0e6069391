body {
  margin: 0;
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    "Fira Sans",
    "Droid Sans",
    "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family:
    source-code-pro,
    Menlo,
    Monaco,
    Consolas,
    "Courier New",
    monospace;
}

.logo {
  height: 27px;
  margin: 17px 0 16px 15px;
  float: left;
}

.ant-table.ant-table-middle .ant-table-title,
.ant-table.ant-table-middle .ant-table-footer,
.ant-table.ant-table-middle thead > tr > th,
.ant-table.ant-table-middle tbody > tr > td {
  padding: 1px 8px !important;
}

.ant-list-sm .ant-list-item {
  padding: 2px !important;
}

.ant-drawer-body {
  padding: 0 !important;
  overflow: hidden !important;
}

.custom-link:hover {
  color: rgb(64 64 64) !important;
}

.full-height-editor {
  height: 100%;
}

.full-height-editor [class*="CodeMirror"] {
  height: 100%;
}

.no-horizontal-scroll-editor [class*="CodeMirror-hscrollbar"] {
  display: none !important;
}

.no-horizontal-scroll-editor [class*="CodeMirror-scroll"] {
  overflow-x: hidden !important;
}
