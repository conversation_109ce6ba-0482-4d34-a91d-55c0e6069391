// Copyright 2021 The Casdoor Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

export const DefaultApplication = "app-built-in";

export const CasvisorUrl = "";

export const ShowGithubCorner = false;
export const IsDemoMode = false;

export const ForceLanguage = "";
export const DefaultLanguage = "en";

export const InitThemeAlgorithm = true;
export const ThemeDefault = {
  themeType: "default",
  colorPrimary: "#5734d3",
  borderRadius: 6,
  isCompact: false,
};

export const CustomFooter = null;

// Blank or null to hide Ai Assistant button
export const AiAssistantUrl = "https://ai.casbin.com";
