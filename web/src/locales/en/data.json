{"account": {"Logout": "Logout", "My Account": "My Account", "Sign Up": "Sign Up"}, "adapter": {"Duplicated policy rules": "Duplicated policy rules", "Edit Adapter": "Edit Adapter", "Failed to sync policies": "Failed to sync policies", "New Adapter": "New Adapter", "Policies": "Policies", "Policies - Tooltip": "Casbin policy rules", "Rule type": "Rule type", "Sync policies successfully": "Sync policies successfully", "Use same DB": "Use same DB", "Use same DB - Tooltip": "Use the same DB as Casdoor"}, "application": {"Add Face ID": "Add Face ID", "Add Face ID with Image": "Add Face ID with Image", "Always": "Always", "Auto signin": "Auto signin", "Auto signin - Tooltip": "When a logged-in session exists in Casdoor, it is automatically used for application-side login", "Background URL": "Background URL", "Background URL - Tooltip": "URL of the background image used in the login page", "Background URL Mobile": "Background URL Mobile", "Background URL Mobile - Tooltip": "Background URL Mobile - Tooltip", "Big icon": "Big icon", "Binding providers": "Binding providers", "CSS style": "CSS style", "Center": "Center", "Copy SAML metadata URL": "Copy SAML metadata URL", "Copy prompt page URL": "Copy prompt page URL", "Copy signin page URL": "Copy signin page URL", "Copy signup page URL": "Copy signup page URL", "Custom CSS": "Custom CSS", "Custom CSS - Edit": "Custom CSS - Edit", "Custom CSS - Tooltip": "CSS styling of the signup, signin and forget password forms (e.g. adding borders and shadows)", "Custom CSS Mobile": "Custom CSS Mobile", "Custom CSS Mobile - Edit": "Custom CSS Mobile - Edit", "Custom CSS Mobile - Tooltip": "Custom CSS Mobile - Tooltip", "Dynamic": "Dynamic", "Edit Application": "Edit Application", "Enable Email linking": "Enable Email linking", "Enable Email linking - Tooltip": "When using 3rd-party providers to log in, if there is a user in the organization with the same Email, the 3rd-party login method will be automatically associated with that user", "Enable SAML C14N10": "Enable SAML C14N10", "Enable SAML C14N10 - Tooltip": "Use C14N10 instead of C14N11 in SAML", "Enable SAML POST binding": "Enable SAML POST binding", "Enable SAML POST binding - Tooltip": "The HTTP POST binding uses input fields in a HTML form to send SAML messages, Enable when your SP use it", "Enable SAML compression": "Enable SAML compression", "Enable SAML compression - Tooltip": "Whether to compress SAML response messages when Casdoor is used as SAML idp", "Enable side panel": "Enable side panel", "Enable signin session - Tooltip": "Whether Casdoor maintains a session after logging into Casdoor from the application", "Enable signup": "Enable signup", "Enable signup - Tooltip": "Whether to allow users to register a new account", "Failed signin frozen time": "Failed signin frozen time", "Failed signin frozen time - Tooltip": "Failed signin frozen time - Tooltip", "Failed signin limit": "Failed signin limit", "Failed signin limit - Tooltip": "Failed signin limit - Toolt<PERSON>", "Failed to sign in": "Failed to sign in", "File uploaded successfully": "File uploaded successfully", "First, last": "First, last", "Follow organization theme": "Follow organization theme", "Footer HTML": "Footer HTML", "Footer HTML - Edit": "Footer HTML - Edit", "Footer HTML - Tooltip": "Custom the footer of your application", "Forced redirect origin": "Forced redirect origin", "Form position": "Form position", "Form position - Tooltip": "Location of the signup, signin and forget password forms", "Generate Face ID": "Generate Face ID", "Grant types": "Grant types", "Grant types - Tooltip": "Select which grant types are allowed in the OAuth protocol", "Header HTML": "Header <PERSON>", "Header HTML - Edit": "Header HTML - Edit", "Header HTML - Tooltip": "Custom the head tag of your application entry page", "Incremental": "Incremental", "Inline": "Inline", "Input": "Input", "Internet-Only": "Internet-Only", "Invalid characters in application name": "Invalid characters in application name", "Invitation code": "Invitation code", "Left": "Left", "Logged in successfully": "Logged in successfully", "Logged out successfully": "Logged out successfully", "MFA remember time": "MFA remember time", "MFA remember time - Tooltip": "Configures the duration that a account is remembered as trusted after a successful MFA login", "Multiple Choices": "Multiple Choices", "New Application": "New Application", "No verification": "No verification", "Normal": "Normal", "Only signup": "Only signup", "Org choice mode": "Org choice mode", "Org choice mode - Tooltip": "Org choice mode - Tooltip", "Please enable \\\"Signin session\\\" first before enabling \\\"Auto signin\\\"": "Please enable \\\"Signin session\\\" first before enabling \\\"Auto signin\\\"", "Please input your application!": "Please input your application!", "Please input your organization!": "Please input your organization!", "Please select a HTML file": "Please select a HTML file", "Pop up": "Pop up", "Random": "Random", "Real name": "Real name", "Redirect URL": "Redirect URL", "Redirect URL (Assertion Consumer Service POST Binding URL) - Tooltip": "Redirect URL (Assertion Consumer Service POST Binding URL)", "Redirect URLs": "Redirect URLs", "Redirect URLs - Tooltip": "Allowed redirect URL list, supporting regular expression matching; URLs not in the list will fail to redirect", "Refresh token expire": "Refresh token expire", "Refresh token expire - Tooltip": "Refresh token expiration time", "Reset to Empty": "Reset to Empty", "Right": "Right", "Rule": "Rule", "SAML metadata": "SAML metadata", "SAML metadata - Tooltip": "The metadata of SAML protocol", "SAML reply URL": "SAML reply URL", "Select": "Select", "Side panel HTML": "Side panel HTML", "Side panel HTML - Edit": "Side panel HTML - Edit", "Side panel HTML - Tooltip": "Customize the HTML code for the side panel of the login page", "Sign Up Error": "Sign Up Error", "Signin": "Signin", "Signin (Default True)": "Signin (De<PERSON>ult True)", "Signin items": "Signin items", "Signin items - Tooltip": "Items for users to fill in when signing up", "Signin methods": "Signin methods", "Signin methods - Tooltip": "Signin methods - Tooltip", "Signin session": "Signin session", "Signup items": "Signup items", "Signup items - Tooltip": "Items for users to fill in when registering new accounts", "Single Choice": "Single Choice", "Small icon": "Small icon", "Tags - Tooltip": "Only users with the tag that is listed in the application tags can login", "The application does not allow to sign up new account": "The application does not allow to sign up new account", "Token expire": "Token expire", "Token expire - Tooltip": "Access token expiration time", "Token fields": "Token fields", "Token fields - Tooltip": "The user fields included in the token", "Token format": "Token format", "Token format - Tooltip": "The format of access token", "Token signing method": "Token signing method", "Token signing method - Tooltip": "Signing method of JWT token, needs to be the same algorithm as the certificate", "Use Email as NameID": "Use Email as NameID", "Use Email as NameID - Tooltip": "Use Email as NameID - Tooltip", "You are unexpected to see this prompt page": "You are unexpected to see this prompt page"}, "cert": {"Bit size": "Bit size", "Bit size - Tooltip": "Secret key length", "Certificate": "Certificate", "Certificate - Tooltip": "Public key certificate, used for decrypting the JWT signature of the Access Token. This certificate usually needs to be deployed on the Casdoor SDK side (i.e., the application) to parse the JWT", "Copy certificate": "Copy certificate", "Copy private key": "Copy private key", "Crypto algorithm": "Crypto algorithm", "Crypto algorithm - Tooltip": "Encryption algorithm used by the certificate", "Download certificate": "Download certificate", "Download private key": "Download private key", "Edit Cert": "Edit Cert", "Expire in years": "Expire in years", "Expire in years - Tooltip": "Validity period of the certificate, in years", "New Cert": "New Cert", "Private key": "Private key", "Private key - Tooltip": "Private key corresponding to the public key certificate", "Scope - Tooltip": "Usage scenarios of the certificate", "Type - Tooltip": "Type of certificate"}, "code": {"Code you received": "Code you received", "Email code": "Email code", "Empty code": "Empty code", "Enter your code": "Enter your code", "Phone code": "Phone code", "Please input your phone verification code!": "Please input your phone verification code!", "Please input your verification code!": "Please input your verification code!", "Send Code": "Send Code", "Sending": "Sending", "Submit and complete": "Submit and complete"}, "currency": {"AUD": "AUD", "BRL": "BRL", "CAD": "CAD", "CHF": "CHF", "CNY": "CNY", "CZK": "CZK", "DKK": "DKK", "EUR": "EUR", "GBP": "GBP", "HKD": "HKD", "HUF": "HUF", "INR": "INR", "JPY": "JPY", "KRW": "KRW", "MXN": "MXN", "MYR": "MYR", "NOK": "NOK", "PLN": "PLN", "RUB": "RUB", "SEK": "SEK", "SGD": "SGD", "THB": "THB", "TRY": "TRY", "TWD": "TWD", "USD": "USD", "ZAR": "ZAR"}, "enforcer": {"Edit Enforcer": "Edit Enforcer", "New Enforcer": "New Enforcer"}, "forget": {"Account": "Account", "Change Password": "Change Password", "Choose email or phone": "Choose email or phone", "Next Step": "Next Step", "Please input your username!": "Please input your username!", "Reset": "Reset", "Reset password": "Reset password", "Unknown forget type": "Unknown forget type", "Verify": "Verify"}, "general": {"AI Assistant": "AI Assistant", "API key": "API key", "API key - Tooltip": "API key - Tooltip", "Access key": "Access key", "Access key - Tooltip": "Access key - Tooltip", "Access secret": "Access secret", "Access secret - Tooltip": "Access secret - Tooltip", "Access token is empty": "Access token is empty", "Action": "Action", "Adapter": "Adapter", "Adapter - Tooltip": "Table name of the policy store", "Adapters": "Adapters", "Add": "Add", "Add custom item": "Add custom item", "Admin": "Admin", "Affiliation URL": "Affiliation URL", "Affiliation URL - Tooltip": "The homepage URL for the affiliation", "All": "All", "Application": "Application", "Application - Tooltip": "Application - Tooltip", "Applications": "Applications", "Applications that require authentication": "Applications that require authentication", "Apps": "Apps", "Authorization": "Authorization", "Avatar": "Avatar", "Avatar - Tooltip": "Public avatar image for the user", "Back": "Back", "Back Home": "Back Home", "Business & Payments": "Business & Payments", "Cancel": "Cancel", "Captcha": "<PERSON><PERSON>", "Cert": "Cert", "Cert - Tooltip": "The public key certificate that needs to be verified by the client SDK corresponding to this application", "Certs": "Certs", "Click to Upload": "Click to Upload", "Client IP": "Client IP", "Close": "Close", "Confirm": "Confirm", "Copied to clipboard successfully": "Copied to clipboard successfully", "Created time": "Created time", "Custom": "Custom", "Dashboard": "Dashboard", "Data": "Data", "Default": "<PERSON><PERSON><PERSON>", "Default application": "Default application", "Default application - Tooltip": "Default application for users registered directly from the organization page", "Default avatar": "Default avatar", "Default avatar - Tooltip": "Default avatar used when newly registered users do not set an avatar image", "Default password": "Default password", "Default password - Tooltip": "When adding new user, if the user's password is not specified, the default password will be used as the user's password", "Delete": "Delete", "Description": "Description", "Description - Tooltip": "Detailed description information for reference, Casdoor itself will not use it", "Detail": "Detail", "Disable": "Disable", "Display name": "Display name", "Display name - Tooltip": "A user-friendly, easily readable name displayed publicly in the UI", "Down": "Down", "Edit": "Edit", "Email": "Email", "Email - Tooltip": "Valid email address", "Email only": "Email only", "Email or Phone": "Email or Phone", "Enable": "Enable", "Enable dark logo": "Enable dark logo", "Enable dark logo - Tooltip": "Enable dark logo", "Enable tour": "Enable tour", "Enable tour - Tooltip": "Display tour for users", "Enabled": "Enabled", "Enabled successfully": "Enabled successfully", "Enforcers": "Enforcers", "Failed to add": "Failed to add", "Failed to connect to server": "Failed to connect to server", "Failed to delete": "Failed to delete", "Failed to enable": "Failed to enable", "Failed to get TermsOfUse URL": "Failed to get TermsOfUse URL", "Failed to remove": "Failed to remove", "Failed to save": "Failed to save", "Failed to sync": "Failed to sync", "Failed to verify": "Failed to verify", "False": "False", "Favicon": "Favicon", "Favicon - Tooltip": "Favicon icon URL used in all Casdoor pages of the organization", "First name": "First name", "Forced redirect origin - Tooltip": "Forced redirect origin - Tooltip", "Forget URL": "Forget URL", "Forget URL - Tooltip": "Custom URL for the \"Forget password\" page. If not set, the default Casdoor \"Forget password\" page will be used. When set, the \"Forget password\" link on the login page will redirect to this URL", "Found some texts still not translated? Please help us translate at": "Found some texts still not translated? Please help us translate at", "Go to enable": "Go to enable", "Go to writable demo site?": "Go to writable demo site?", "Groups": "Groups", "Groups - Tooltip": "Groups - Tooltip", "Hide password": "Hide password", "Home": "Home", "Home - Tooltip": "Home page of the application", "ID": "ID", "ID - Tooltip": "Unique random string", "IP whitelist": "IP whitelist", "IP whitelist - Tooltip": "IP whitelist - Tooltip", "Identity": "Identity", "Invitations": "Invitations", "Is enabled": "Is enabled", "Is enabled - Tooltip": "Set whether it can use", "Is shared": "Is shared", "Is shared - Tooltip": "Share this application with other organizations", "LDAPs": "LDAPs", "LDAPs - Tooltip": "LDAP servers", "Languages": "Languages", "Languages - Tooltip": "Available languages", "Last name": "Last name", "Later": "Later", "Logging & Auditing": "Logging & Auditing", "Logo": "Logo", "Logo - Tooltip": "Icons that the application presents to the outside world", "Logo dark": "Logo dark", "Logo dark - Tooltip": "The logo used in dark theme", "MFA items": "MFA items", "MFA items - Tooltip": "MFA items - Tooltip", "Master password": "Master password", "Master password - Tooltip": "Can be used to log in to all users under this organization, making it convenient for administrators to log in as this user to solve technical issues", "Master verification code": "Master verification code", "Master verification code - Tooltip": "When the master verification code is set, all email and SMS verification codes sent under this organization will use this fixed code. It's mainly used for automated testing and CI purposes and is generally not used in normal environments", "Menu": "<PERSON><PERSON>", "Method": "Method", "Model": "Model", "Model - Tooltip": "Casbin access control model", "Models": "Models", "Name": "Name", "Name - Tooltip": "Unique, string-based ID", "Name format": "Name format", "Non-LDAP": "Non-LDAP", "None": "None", "OAuth providers": "OAuth providers", "OK": "OK", "Organization": "Organization", "Organization - Tooltip": "Similar to concepts such as tenants or user pools, each user and application belongs to an organization", "Organizations": "Organizations", "Password": "Password", "Password - Tooltip": "Make sure the password is correct", "Password complexity options": "Password complexity options", "Password complexity options - Tooltip": "Different combinations of password complexity options", "Password obf key": "Password obf key", "Password obf key - Tooltip": "Password obf key - <PERSON><PERSON><PERSON>", "Password obfuscator": "Password obfuscator", "Password obfuscator - Tooltip": "Password obfuscator - Toolt<PERSON>", "Password salt": "Password salt", "Password salt - Tooltip": "Random parameter used for password encryption", "Password type": "Password type", "Password type - Tooltip": "Storage format of passwords in the database", "Payment": "Payment", "Payment - Tooltip": "Payment - Tooltip", "Payments": "Payments", "Permissions": "Permissions", "Permissions - Tooltip": "Permissions owned by this user", "Phone": "Phone", "Phone - Tooltip": "Phone number", "Phone only": "Phone only", "Phone or Email": "Phone or Email", "Plain": "Plain", "Plan": "Plan", "Plan - Tooltip": "Plan - Toolt<PERSON>", "Plans": "Plans", "Plans - Tooltip": "Plans - Tooltip", "Preview": "Preview", "Preview - Tooltip": "Preview the configured effects", "Pricing": "Pricing", "Pricing - Tooltip": "Pricing - Tooltip", "Pricings": "Pricings", "Products": "Products", "Provider": "Provider", "Provider - Tooltip": "Payment providers to be configured, including PayPal, Alipay, WeChat Pay, etc.", "Providers": "Providers", "Providers - Tooltip": "Providers to be configured, including 3rd-party login, object storage, verification code, etc.", "QR Code": "QR Code", "QR code is too large": "QR code is too large", "Real name": "Real name", "Records": "Records", "Request": "Request", "Request URI": "Request URI", "Resources": "Resources", "Role": "Role", "Role - Tooltip": "Role - <PERSON><PERSON><PERSON>", "Roles": "Roles", "Roles - Tooltip": "Roles that the user belongs to", "Root cert": "Root cert", "Root cert - Tooltip": "Root cert - Tooltip", "SAML attributes": "SAML attributes", "SAML attributes - Tooltip": "SAML attributes - Tooltip", "SSH cert": "SSH cert", "SSH type": "SSH type", "SSH type - Tooltip": "The auth type of SSH connection", "Save": "Save", "Save & Exit": "Save & Exit", "Session ID": "Session ID", "Sessions": "Sessions", "Shortcuts": "Shortcuts", "Signin URL": "Signin URL", "Signin URL - Tooltip": "Custom URL for the login page. If not set, the default Casdoor login page will be used. When set, the login links on various Casdoor pages will redirect to this URL", "Signup URL": "Signup URL", "Signup URL - Tooltip": "Custom URL for the registration page. If not set, the default Casdoor registration page will be used. When set, the registration links on various Casdoor pages will redirect to this URL", "Signup application": "Signup application", "Signup application - Tooltip": "Which application the user registered through when they signed up", "Signup link": "Signup link", "Sorry, the page you visited does not exist.": "Sorry, the page you visited does not exist.", "Sorry, the user you visited does not exist or you are not authorized to access this user.": "Sorry, the user you visited does not exist or you are not authorized to access this user.", "Sorry, you do not have permission to access this page or logged in status invalid.": "Sorry, you do not have permission to access this page or logged in status invalid.", "State": "State", "State - Tooltip": "State", "Subscriptions": "Subscriptions", "Successfully added": "Successfully added", "Successfully deleted": "Successfully deleted", "Successfully removed": "Successfully removed", "Successfully saved": "Successfully saved", "Successfully sent": "Successfully sent", "Successfully synced": "Successfully synced", "Supported country codes": "Supported country codes", "Supported country codes - Tooltip": "Country codes supported by the organization. These codes can be selected as a prefix when sending SMS verification codes", "Sure to delete": "Sure to delete", "Sure to disable": "Sure to disable", "Sure to remove": "Sure to remove", "Swagger": "Swagger", "Sync": "Sync", "Syncers": "Syncers", "System Info": "System Info", "There was a problem signing you in..": "There was a problem signing you in..", "This is a read-only demo site!": "This is a read-only demo site!", "Timestamp": "Timestamp", "Tokens": "Tokens", "Tour": "Tour", "Transactions": "Transactions", "True": "True", "Type": "Type", "Type - Tooltip": "Type - Tooltip", "URL": "URL", "URL - Tooltip": "URL link", "Up": "Up", "Updated time": "Updated time", "User": "User", "User - Tooltip": "Make sure the username is correct", "User Management": "User Management", "User containers": "User pools", "User type": "User type", "User type - Tooltip": "Tags that the user belongs to, defaulting to \"normal-user\"", "Users": "Users", "Users - Tooltip": "Users - Tooltip", "Users under all organizations": "Users under all organizations", "Verifications": "Verifications", "Webhooks": "Webhooks", "You can only select one physical group": "You can only select one physical group", "empty": "empty", "remove": "remove", "{total} in total": "{total} in total"}, "group": {"Edit Group": "Edit Group", "New Group": "New Group", "Parent group": "Parent group", "Parent group - Tooltip": "Parent group - Tooltip", "Physical": "Physical", "Show all": "Show all", "Upload (.xlsx)": "Upload (.xlsx)", "Virtual": "Virtual", "You need to delete all subgroups first. You can view the subgroups in the left group tree of the [Organizations] -> [Groups] page": "You need to delete all subgroups first. You can view the subgroups in the left group tree of the [Organizations] -> [Groups] page"}, "home": {"New users past 30 days": "New users past 30 days", "New users past 7 days": "New users past 7 days", "New users today": "New users today", "Past 30 Days": "Past 30 Days", "Total users": "Total users"}, "invitation": {"Code": "Code", "Code - Tooltip": "Can be a single string as an invitation code, or a regular expression. All strings matching the regular expression are valid invitation codes", "Default code": "Default code", "Default code - Tooltip": "When the invitation code is a regular expression, please enter the invitation code that matches the regular expression rule as the default invitation code for the invitation link", "Edit Invitation": "Edit Invitation", "New Invitation": "New Invitation", "Quota": "<PERSON><PERSON><PERSON>", "Quota - Tooltip": "The maximum number of users that can register using this invitation code", "Used count": "Used count", "Used count - Tooltip": "The number of times this invitation code has been used", "You need to first specify a default application for organization: ": "You need to first specify a default application for organization: "}, "ldap": {"Admin": "Admin", "Admin - Tooltip": "CN or ID of the LDAP server administrator", "Admin Password": "Admin Password", "Admin Password - Tooltip": "LDAP server administrator password", "Allow self-signed certificate": "Allow self-signed certificate", "Allow self-signed certificate - Tooltip": "Allow self-signed certificate - Tooltip", "Auto Sync": "Auto Sync", "Auto Sync - Tooltip": "Auto-sync configuration, disabled at 0", "Base DN": "Base DN", "Base DN - Tooltip": "Base DN during LDAP search", "CN": "CN", "Default group": "Default group", "Default group - Tooltip": "Group to which users belong after synchronization", "Edit LDAP": "Edit LDAP", "Enable SSL": "Enable SSL", "Enable SSL - Tooltip": "Whether to enable SSL", "Filter fields": "Filter fields", "Filter fields - Tooltip": "Filter fields - Tooltip", "Group ID": "Group ID", "Last Sync": "Last Sync", "Search Filter": "Search Filter", "Search Filter - Tooltip": "Search Filter - Tooltip", "Server": "Server", "Server host": "Server host", "Server host - Tooltip": "LDAP server address", "Server name": "Server name", "Server name - Tooltip": "LDAP server configuration display name", "Server port": "Server port", "Server port - Tooltip": "LDAP server port", "The Auto Sync option will sync all users to specify organization": "The Auto Sync option will sync all users to specify organization", "synced": "synced", "unsynced": "unsynced"}, "login": {"Auto sign in": "Auto sign in", "Back button": "Back button", "Continue with": "Continue with", "Email": "Email", "Email or phone": "Email or phone", "Face ID": "Face ID", "Face Recognition": "Face Recognition", "Face recognition failed": "Face recognition failed", "Failed to log out": "Failed to log out", "Failed to obtain MetaMask authorization": "Failed to obtain MetaMask authorization", "Failed to obtain Web3-Onboard authorization": "Failed to obtain Web3-Onboard authorization", "Forgot password?": "Forgot password?", "LDAP": "LDAP", "LDAP username, Email or phone": "LDAP username, Email or phone", "Loading": "Loading", "Logging out...": "Logging out...", "MetaMask plugin not detected": "MetaMask plugin not detected", "Model loading failure": "Model loading failure", "No account?": "No account?", "Or sign in with another account": "Or sign in with another account", "Phone": "Phone", "Please ensure sufficient lighting and align your face in the center of the recognition box": "Please ensure sufficient lighting and align your face in the center of the recognition box", "Please ensure that you have a camera device for facial recognition": "Please ensure that you have a camera device for facial recognition", "Please input your Email or Phone!": "Please input your Email or Phone!", "Please input your Email!": "Please input your Email!", "Please input your LDAP username!": "Please input your LDAP username!", "Please input your Phone!": "Please input your Phone!", "Please input your code!": "Please input your code!", "Please input your organization name!": "Please input your organization name!", "Please input your password!": "Please input your password!", "Please load the webpage using HTTPS, otherwise the camera cannot be accessed": "Please load the webpage using HTTPS, otherwise the camera cannot be accessed", "Please provide permission to access the camera": "Please provide permission to access the camera", "Please select an organization": "Please select an organization", "Please select an organization to sign in": "Please select an organization to sign in", "Please type an organization to sign in": "Please type an organization to sign in", "Redirecting, please wait.": "Redirecting, please wait.", "Refresh": "Refresh", "Sign In": "Sign In", "Sign in with Face ID": "Sign in with Face ID", "Sign in with WebAuthn": "Sign in with WebAuthn", "Sign in with {type}": "Sign in with {type}", "Signin button": "Signin button", "Signing in...": "Signing in...", "Successfully logged in with WebAuthn credentials": "Successfully logged in with WebAuthn credentials", "The camera is currently in use by another webpage": "The camera is currently in use by another webpage", "The input is not valid Email or phone number!": "The input is not valid Email or phone number!", "The input is not valid Email!": "The input is not valid Email!", "The input is not valid phone number!": "The input is not valid phone number!", "To access": "To access", "Verification code": "Verification code", "WeChat": "WeChat", "WebAuthn": "WebAuthn", "sign up now": "sign up now", "username, Email or phone": "username, Email or phone"}, "mfa": {"Each time you sign in to your Account, you'll need your password and a authentication code": "Each time you sign in to your Account, you'll need your password and a authentication code", "Enable multi-factor authentication": "Enable multi-factor authentication", "Failed to get application": "Failed to get application", "Failed to initiate MFA": "Failed to initiate MFA", "Have problems?": "Have problems?", "Multi-factor authentication": "Multi-factor authentication", "Multi-factor authentication - Tooltip ": "Multi-factor authentication - Tooltip ", "Multi-factor methods": "Multi-factor methods", "Multi-factor recover": "Multi-factor recover", "Multi-factor recover description": "Multi-factor recover description", "Or copy the secret to your Authenticator App": "Or copy the secret to your Authenticator App", "Please bind your email first, the system will automatically uses the mail for multi-factor authentication": "Please bind your email first, the system will automatically uses the mail for multi-factor authentication", "Please bind your phone first, the system automatically uses the phone for multi-factor authentication": "Please bind your phone first, the system automatically uses the phone for multi-factor authentication", "Please confirm the information below": "Please confirm the information below", "Please save this recovery code. Once your device cannot provide an authentication code, you can reset mfa authentication by this recovery code": "Please save this recovery code. Once your device cannot provide an authentication code, you can reset mfa authentication by this recovery code", "Protect your account with Multi-factor authentication": "Protect your account with Multi-factor authentication", "Recovery code": "Recovery code", "Remember this account for {hour} hours": "Remember this account for {hour} hours", "Scan the QR code with your Authenticator App": "Scan the QR code with your Authenticator App", "Set preferred": "Set preferred", "Setup": "Setup", "To ensure the security of your account, it is recommended that you enable multi-factor authentication": "To ensure the security of your account, it is recommended that you enable multi-factor authentication", "To ensure the security of your account, it is required to enable multi-factor authentication": "To ensure the security of your account, it is required to enable multi-factor authentication", "Use Authenticator App": "Use Authenticator App", "Use Email": "Use Email", "Use SMS": "Use SMS", "Use SMS verification code": "Use SMS verification code", "Use a recovery code": "Use a recovery code", "Verify Code": "Verify Code", "Verify Password": "Verify Password", "You have enabled Multi-Factor Authentication, Please click 'Send Code' to continue": "You have enabled Multi-Factor Authentication, Please click 'Send Code' to continue", "You have enabled Multi-Factor Authentication, please enter the TOTP code": "You have enabled Multi-Factor Authentication, please enter the TOTP code", "Your email is": "Your email is", "Your phone is": "Your phone is", "preferred": "preferred"}, "model": {"Advanced Editor": "Advanced Editor", "Basic Editor": "Basic Editor", "Edit Model": "Edit Model", "Model text": "Model text", "Model text - Tooltip": "Casbin access control model, including built-in models like ACL, RBAC, ABAC, RESTful, etc. You can also create custom models. For more information, please visit the Casbin website", "New Model": "New Model"}, "organization": {"Account items": "Account items", "Account items - Tooltip": "Items in the Personal settings page", "All": "All", "Edit Organization": "Edit Organization", "Follow global theme": "Follow global theme", "Has privilege consent": "Has privilege consent", "Has privilege consent - Tooltip": "Prevent adding users for built-in organization if HasPrivilegeConsent is false", "Has privilege consent warning": "Adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.", "Init score": "Init score", "Init score - Tooltip": "Initial score points awarded to users upon registration", "Is profile public": "Is profile public", "Is profile public - Tooltip": "After being closed, only global administrators or users in the same organization can access the user's profile page", "Modify rule": "Modify rule", "Navbar items": "Navbar items", "Navbar items - Tooltip": "Navbar items - Tooltip", "New Organization": "New Organization", "Optional": "Optional", "Password expire days": "Password expire days", "Password expire days - Tooltip": "Password expire days - <PERSON><PERSON><PERSON>", "Prompt": "Prompt", "Required": "Required", "Soft deletion": "Soft deletion", "Soft deletion - Tooltip": "When enabled, deleting users will not completely remove them from the database. Instead, they will be marked as deleted", "Tags": "Tags", "Tags - Tooltip": "Collection of tags available for users to choose from", "Use Email as username": "Use Email as username", "Use Email as username - Tooltip": "Use Email as username if the username field is not visible at signup", "User types": "User types", "User types - Tooltip": "User types - Tooltip", "View rule": "View rule", "Visible": "Visible", "Website URL": "Website URL", "Website URL - Tooltip": "The homepage URL of the organization. This field is not used in Casdoor", "Widget items": "Widget items", "Widget items - Tooltip": "Widget items - Tooltip"}, "payment": {"Confirm your invoice information": "Confirm your invoice information", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Currency - Tooltip": "Like USD, CNY, etc.", "Download Invoice": "Download Invoice", "Edit Payment": "Edit Payment", "Failed reason": "Failed reason", "Individual": "Individual", "Invoice URL": "Invoice URL", "Invoice URL - Tooltip": "URL for downloading the invoice", "Invoice actions": "Invoice actions", "Invoice actions - Tooltip": "Operations include issuing invoices and downloading invoices", "Invoice remark": "Invoice remark", "Invoice remark - Tooltip": "The remark should not exceed 50 characters", "Invoice tax ID": "Invoice tax ID", "Invoice tax ID - Tooltip": "When the invoice type is for an organization, the organization taxpayer identification number must be entered; when the invoice type is for an individual, it is not necessary to fill in this information", "Invoice title": "Invoice title", "Invoice title - Tooltip": "When the invoice type is for an organization, the invoice title can be entered as the organization name; when the invoice type is for an individual, the system will automatically fill in the payer's name", "Invoice type": "Invoice type", "Invoice type - Tooltip": "The invoice type can be for an individual or an organization", "Issue Invoice": "Issue Invoice", "Message": "Message", "Message - Tooltip": "Payment processing result message", "New Payment": "New Payment", "Person Email": "Person Email", "Person Email - Tooltip": "Email of the payer", "Person ID card": "Person ID card", "Person ID card - Tooltip": "ID card number of the payer", "Person name": "Person name", "Person name - Tooltip": "Real name of the payer", "Person phone": "Person phone", "Person phone - Tooltip": "The phone number of the payer", "Please carefully check your invoice information. Once the invoice is issued, it cannot be withdrawn or modified.": "Please carefully check your invoice information. Once the invoice is issued, it cannot be withdrawn or modified.", "Please click the below button to return to the original website": "Please click the below button to return to the original website", "Please pay the order first!": "Please pay the order first!", "Processing...": "Processing...", "Product": "Product", "Product - Tooltip": "Product Name", "Recharged successfully": "Recharged successfully", "Result": "Result", "Return to Website": "Return to Website", "The payment has been canceled": "The payment has been canceled", "The payment has failed": "The payment has failed", "The payment has time out": "The payment has time out", "The payment is still under processing": "The payment is still under processing", "Type - Tooltip": "Payment method used when purchasing the product", "You have successfully completed the payment": "You have successfully completed the payment", "You have successfully recharged": "You have successfully recharged", "Your current balance is": "Your current balance is", "please wait for a few seconds...": "please wait for a few seconds...", "the current state is": "the current state is"}, "permission": {"Actions": "Actions", "Actions - Tooltip": "Allowed actions", "Admin": "Admin", "Allow": "Allow", "Approve time": "Approve time", "Approve time - Tooltip": "The time of approval for this permission", "Approved": "Approved", "Approver": "Approver", "Approver - Tooltip": "The person who approved the permission", "Deny": "<PERSON><PERSON>", "Edit Permission": "Edit Permission", "Effect": "Effect", "Effect - Tooltip": "Allow or reject", "New Permission": "New Permission", "Pending": "Pending", "Read": "Read", "Resource type": "Resource type", "Resource type - Tooltip": "Type of resource", "Resources - Tooltip": "Authorized resources", "Submitter": "Submitter", "Submitter - Tooltip": "The person applying for this permission", "TreeNode": "TreeNode", "Write": "Write"}, "plan": {"Edit Plan": "Edit Plan", "New Plan": "New Plan", "Period": "Period", "Period - Tooltip": "Period for the plan", "Price": "Price", "Price - Tooltip": "Price needs to pay to subscribe the plan", "Related product": "Related product", "per month": "per month", "per year": "per year"}, "pricing": {"Copy pricing page URL": "Copy pricing page URL", "Edit Pricing": "Edit Pricing", "Failed to get plans": "Failed to get plans", "Free": "Free", "Getting started": "Getting started", "New Pricing": "New Pricing", "Trial duration": "Trial duration", "Trial duration - Tooltip": "Trial duration period", "days trial available!": "days trial available!", "paid-user do not have active subscription or pending subscription, please select a plan to buy": "paid-user do not have active subscription or pending subscription, please select a plan to buy"}, "product": {"AirWallex": "AirWallex", "Alipay": "Alipay", "Buy": "Buy", "Buy Product": "Buy Product", "Detail": "Detail", "Detail - Tooltip": "Detail of product", "Dummy": "Dummy", "Edit Product": "Edit Product", "Image": "Image", "Image - Tooltip": "Image of product", "Is recharge": "Is recharge", "Is recharge - Tooltip": "Whether the current product is to recharge balance", "New Product": "New Product", "Pay": "Pay", "PayPal": "PayPal", "Payment cancelled": "Payment cancelled", "Payment failed": "Payment failed", "Payment providers": "Payment providers", "Payment providers - Tooltip": "Providers of payment services", "Placing order...": "Placing order...", "Price": "Price", "Price - Tooltip": "Price of product", "Quantity": "Quantity", "Quantity - Tooltip": "Quantity of product", "Return URL": "Return URL", "Return URL - Tooltip": "URL to return to after successful purchase", "SKU": "SKU", "Sold": "Sold", "Sold - Tooltip": "Quantity sold", "Stripe": "Stripe", "Success URL": "Success URL", "Success URL - Tooltip": "URL to return to after purchase", "Tag - Tooltip": "Tag of product", "Test buy page..": "Test buy page..", "There is no payment channel for this product.": "There is no payment channel for this product.", "This product is currently not in sale.": "This product is currently not in sale.", "WeChat Pay": "WeChat Pay"}, "provider": {"Access key": "Access key", "Access key - Tooltip": "Access key", "Agent ID": "Agent ID", "Agent ID - Tooltip": "Agent ID", "Api Key": "Api Key", "Api Key - Tooltip": "Api Key - Tooltip", "App ID": "App ID", "App ID - Tooltip": "App ID", "App Key": "App Key", "App Key - Tooltip": "App Key - Tooltip", "App key": "App key", "App key - Tooltip": "App key", "App secret": "App secret", "AppSecret - Tooltip": "App secret", "Auth Key": "Auth Key", "Auth Key - Tooltip": "Auth Key - Tooltip", "Auth URL": "Auth URL", "Auth URL - Tooltip": "Auth URL", "Base URL": "Base URL", "Base URL - Tooltip": "Base URL - Tooltip", "Bucket": "Bucket", "Bucket - Tooltip": "Name of bucket", "Can not parse metadata": "Can not parse metadata", "Can signin": "Can signin", "Can signup": "Can signup", "Can unlink": "Can unlink", "Category": "Category", "Category - Tooltip": "Select a category", "Channel No.": "Channel No.", "Channel No. - Tooltip": "Channel No.", "Chat ID": "Chat ID", "Chat ID - Tooltip": "Chat ID - Tooltip", "Client ID": "Client ID", "Client ID - Tooltip": "Client ID", "Client ID 2": "Client ID 2", "Client ID 2 - Tooltip": "The second Client ID", "Client secret": "Client secret", "Client secret - Tooltip": "Client secret", "Client secret 2": "Client secret 2", "Client secret 2 - Tooltip": "The second client secret key", "Content": "Content", "Content - Tooltip": "Content - Tooltip", "Copy": "Copy", "DB test": "DB test", "DB test - Tooltip": "Test the connectivity to the database", "Disable SSL": "Disable SSL", "Disable SSL - Tooltip": "Whether to disable SSL protocol when communicating with STMP server", "Domain": "Domain", "Domain - Tooltip": "Custom domain for object storage", "Edit Provider": "Edit Provider", "Email content": "Email content", "Email content - Tooltip": "Content of the Email", "Email regex": "<PERSON><PERSON> regex", "Email regex - Tooltip": "Email regex - Tooltip", "Email title": "Email title", "Email title - Tooltip": "Title of the email", "Endpoint": "Endpoint", "Endpoint (Intranet)": "Endpoint (Intranet)", "Endpoint - Tooltip": "Endpoint - Tooltip", "Follow-up action": "Follow-up action", "Follow-up action - Tooltip": "If you choose \"Use WeChat Open Platform to login\", users need to login on the WeChat Open Platform after following the wechat official account.", "From address": "From address", "From address - Tooltip": "Email address of \"From\"", "From name": "From name", "From name - Tooltip": "Name of \"From\"", "Get phone number": "Get phone number", "Get phone number - Tooltip": "If sync phone number is enabled, you should enable google people api first and add scope https://www.googleapis.com/auth/user.phonenumbers.read", "HTTP body mapping": "HTTP body mapping", "HTTP body mapping - Tooltip": "HTTP body mapping", "HTTP header": "HTTP header", "HTTP header - Tooltip": "HTTP header - Tooltip", "Host": "Host", "Host - Tooltip": "Name of host", "IdP": "IdP", "IdP certificate": "IdP certificate", "Internal": "Internal", "Issuer URL": "Issuer URL", "Issuer URL - Tooltip": "Issuer URL", "Key ID": "Key ID", "Key ID - Tooltip": "Key ID", "Key text": "Key text", "Key text - Tooltip": "Key text", "Metadata": "<PERSON><PERSON><PERSON>", "Metadata - Tooltip": "SAML metadata", "Metadata url": "Metadata url", "Metadata url - Tooltip": "Metadata url - Tooltip", "Method - Tooltip": "Login method, QR code or silent login", "New Provider": "New Provider", "Normal": "Normal", "Parameter": "Parameter", "Parameter - Tooltip": "Parameter - Tooltip", "Parse": "Parse", "Parse metadata successfully": "Parse metadata successfully", "Path prefix": "Path prefix", "Path prefix - Tooltip": "Bucket path prefix for object storage", "Please use WeChat to scan the QR code and follow the official account for sign in": "Please use WeChat to scan the QR code and follow the official account for sign in", "Port": "Port", "Port - Tooltip": "Make sure the port is open", "Private Key": "Private Key", "Private Key - Tooltip": "Private Key - Tooltip", "Project Id": "Project Id", "Project Id - Tooltip": "Project Id - Tooltip", "Prompted": "Prompted", "Provider - Tooltip": "Provider - <PERSON>lt<PERSON>", "Provider URL": "Provider URL", "Provider URL - Tooltip": "URL for configuring the service provider, this field is only used for reference and is not used in Casdoor", "Public key": "Public key", "Public key - Tooltip": "Public key - Tooltip", "Region": "Region", "Region - Tooltip": "Region - Tooltip", "Region ID": "Region ID", "Region ID - Tooltip": "Region ID for the service provider", "Region endpoint for Internet": "Region endpoint for Internet", "Region endpoint for Intranet": "Region endpoint for Intranet", "Required": "Required", "Reset to Default HTML": "Reset to Default HTML", "Reset to Default Text": "Reset to Default Text", "SAML 2.0 Endpoint (HTTP)": "SAML 2.0 Endpoint (HTTP)", "SMS Test": "SMS Test", "SMS Test - Tooltip": "Phone number for sending test SMS", "SMS account": "SMS account", "SMS account - Tooltip": "SMS account", "SMTP connected successfully": "SMTP connected successfully", "SP ACS URL": "SP ACS URL", "SP ACS URL - Tooltip": "SP ACS URL", "SP Entity ID": "SP Entity ID", "Scene": "Scene", "Scene - Tooltip": "Scene", "Scope": "<PERSON><PERSON>", "Scope - Tooltip": "<PERSON><PERSON>", "Secret access key": "Secret access key", "Secret access key - Tooltip": "Secret access key", "Secret key": "Secret key", "Secret key - Tooltip": "Used by the server to call the verification code provider API for verification", "Send Testing Email": "Send Testing Email", "Send Testing Notification": "Send Testing Notification", "Send Testing SMS": "Send Testing SMS", "Sender Id": "Sender Id", "Sender Id - Tooltip": "Sender <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "Sender number": "Sender number", "Sender number - Tooltip": "Sender number - <PERSON><PERSON><PERSON>", "Service ID identifier": "Service ID identifier", "Service ID identifier - Tooltip": "Service ID identifier", "Service account JSON": "Service account JSON", "Service account JSON - Tooltip": "The JSON file content for the service account", "Sign Name": "Sign Name", "Sign Name - Tooltip": "Name of the signature to be used", "Sign request": "Sign request", "Sign request - Tooltip": "Whether the request requires a signature", "Signin HTML": "Signin HTML", "Signin HTML - Edit": "Signin HTML - Edit", "Signin HTML - Tooltip": "Custom HTML for replacing the default signin page style", "Signup HTML": "Signup HTML", "Signup HTML - Edit": "Signup HTML - Edit", "Signup HTML - Tooltip": "Custom HTML for replacing the default signup page style", "Signup group": "Signup group", "Signup group - Tooltip": "Signup group - Tooltip", "Silent": "Silent", "Site key": "Site key", "Site key - Tooltip": "Site key", "Sub type": "Sub type", "Sub type - Tooltip": "Sub type", "Subject": "Subject", "Subject - Tooltip": "Subject of email", "Team ID": "Team ID", "Team ID - Tooltip": "Team ID", "Template code": "Template code", "Template code - Tooltip": "Template code", "Test Email": "Test Email", "Test Email - Tooltip": "Email address to receive test mails", "Test SMTP Connection": "Test SMTP Connection", "Third-party": "Third-party", "This field is required": "This field is required", "Token URL": "Token URL", "Token URL - Tooltip": "Token URL", "Type": "Type", "Type - Tooltip": "Select a type", "Use WeChat Media Platform in PC": "Use WeChat Media Platform in PC", "Use WeChat Media Platform in PC - Tooltip": "Whether to allow scanning WeChat Media Platform QR code to login", "Use WeChat Media Platform to login": "Use WeChat Media Platform to login", "Use WeChat Open Platform to login": "Use WeChat Open Platform to login", "Use global endpoint": "Use global endpoint", "Use global endpoint - Tooltip": "Use global endpoint - Tooltip", "Use id as name": "Use id as name", "Use id as name - Tooltip": "Use id as user's name", "User flow": "User flow", "User flow - Tooltip": "User flow", "User mapping": "User mapping", "User mapping - Tooltip": "User mapping - Tooltip", "UserInfo URL": "UserInfo URL", "UserInfo URL - Tooltip": "UserInfo URL", "Wallets": "Wallets", "Wallets - Tooltip": "Wallets - Tooltip", "admin (Shared)": "admin (Shared)"}, "record": {"Is triggered": "Is triggered", "Object": "Object", "Response": "Response", "Status code": "Status code"}, "resource": {"Copy Link": "Copy Link", "File name": "File name", "File size": "File size", "Format": "Format", "Parent": "Parent", "Upload a file...": "Upload a file..."}, "role": {"Edit Role": "Edit Role", "New Role": "New Role", "Sub domains": "Sub domains", "Sub domains - Tooltip": "Domains included in the current role", "Sub groups": "Sub groups", "Sub groups - Tooltip": "Groups included in the current role", "Sub roles": "Sub roles", "Sub roles - Tooltip": "Roles included in the current role", "Sub users": "Sub users", "Sub users - Tooltip": "Users included in the current role"}, "signup": {"Accept": "Accept", "Agreement": "Agreement", "Confirm": "Confirm", "Decline": "Decline", "Have account?": "Have account?", "Label": "Label", "Label HTML": "Label HTML", "Options": "Options", "Placeholder": "Placeholder", "Please accept the agreement!": "Please accept the agreement!", "Please click the below button to sign in": "Please click the below button to sign in", "Please confirm your password!": "Please confirm your password!", "Please input the correct ID card number!": "Please input the correct ID card number!", "Please input your Email!": "Please input your Email!", "Please input your ID card number!": "Please input your ID card number!", "Please input your address!": "Please input your address!", "Please input your affiliation!": "Please input your affiliation!", "Please input your display name!": "Please input your display name!", "Please input your first name!": "Please input your first name!", "Please input your invitation code!": "Please input your invitation code!", "Please input your last name!": "Please input your last name!", "Please input your phone number!": "Please input your phone number!", "Please input your real name!": "Please input your real name!", "Please select your country code!": "Please select your country code!", "Please select your country/region!": "Please select your country/region!", "Regex": "Regex", "Signup button": "Signup button", "Terms of Use": "Terms of Use", "Terms of Use - Tooltip": "Terms of use that users need to read and agree to during registration", "Text 1": "Text 1", "Text 2": "Text 2", "Text 3": "Text 3", "Text 4": "Text 4", "Text 5": "Text 5", "The input Email doesn't match the signup item regex!": "The input Email doesn't match the signup item regex!", "The input is not invoice Tax ID!": "The input is not invoice Tax ID!", "The input is not invoice title!": "The input is not invoice title!", "The input is not valid Email!": "The input is not valid Email!", "The input is not valid Phone!": "The input is not valid Phone!", "Username": "Username", "Username - Tooltip": "Username - Tooltip", "Your account has been created!": "Your account has been created!", "Your confirmed password is inconsistent with the password!": "Your confirmed password is inconsistent with the password!", "sign in now": "sign in now"}, "subscription": {"Active": "Active", "Edit Subscription": "Edit Subscription", "End time": "End time", "End time - Tooltip": "End time of the subscription", "Error": "Error", "Expired": "Expired", "New Subscription": "New Subscription", "Pending": "Pending", "Period": "Period", "Start time": "Start time", "Start time - Tooltip": "Start time of the subscription", "Suspended": "Suspended", "Upcoming": "Upcoming"}, "syncer": {"Affiliation table": "Affiliation table", "Affiliation table - Tooltip": "Database table name of the work unit", "Avatar base URL": "Avatar base URL", "Avatar base URL - Tooltip": "URL prefix for the avatar images", "Casdoor column": "Casdoor column", "Column name": "Column name", "Column type": "Column type", "Connect successfully": "Connect successfully", "Database": "Database", "Database - Tooltip": "The original database name", "Database type": "Database type", "Database type - Tooltip": "Database type, supporting all databases supported by XORM, such as MySQL, PostgreSQL, SQL Server, Oracle, SQLite, etc.", "Edit Syncer": "Edit Syncer", "Error text": "Error text", "Error text - Tooltip": "Error text", "Failed to connect": "Failed to connect", "Is hashed": "Is hashed", "Is key": "Is key", "Is read-only": "Is read-only", "Is read-only - Tooltip": "Is read-only - <PERSON><PERSON><PERSON>", "New Syncer": "<PERSON> Syncer", "SSH host": "SSH host", "SSH password": "SSH password", "SSH port": "SSH port", "SSH user": "SSH user", "SSL mode": "SSL mode", "SSL mode - Tooltip": "The SSL mode used when connecting to the database", "Sync interval": "Sync interval", "Sync interval - Tooltip": "Unit in seconds", "Table": "Table", "Table - Tooltip": "Name of database table", "Table columns": "Table columns", "Table columns - Tooltip": "Columns in the table involved in data synchronization. Columns that are not involved in synchronization do not need to be added", "Test DB Connection": "Test DB Connection"}, "system": {"API Latency": "API Latency", "API Throughput": "API Throughput", "About Casdoor": "About Casdoor", "An Identity and Access Management (IAM) / Single-Sign-On (SSO) platform with web UI supporting OAuth 2.0, OIDC, SAML and CAS": "An Identity and Access Management (IAM) / Single-Sign-On (SSO) platform with web UI supporting OAuth 2.0, OIDC, SAML and CAS", "CPU Usage": "CPU Usage", "Community": "Community", "Count": "Count", "Failed to get CPU usage": "Failed to get CPU usage", "Failed to get memory usage": "Failed to get memory usage", "Latency": "Latency", "Memory Usage": "Memory Usage", "Official website": "Official website", "Throughput": "Throughput", "Total Throughput": "Total Throughput", "Unknown version": "Unknown version", "Version": "Version"}, "theme": {"Blossom": "Blossom", "Border radius": "Border radius", "Compact": "Compact", "Customize theme": "Customize theme", "Dark": "Dark", "Default": "<PERSON><PERSON><PERSON>", "Document": "Document", "Is compact": "Is compact", "Primary color": "Primary color", "Theme": "Theme", "Theme - Tooltip": "Style theme of the application"}, "token": {"Access token": "Access token", "Access token - Tooltip": "Access token - Tooltip", "Authorization code": "Authorization code", "Authorization code - Tooltip": "Authorization code - Tooltip", "Copy access token": "Copy access token", "Copy parsed result": "Copy parsed result", "Edit Token": "<PERSON>", "Expires in": "Expires in", "Expires in - Tooltip": "Expires in - Tooltip", "New Token": "New Token", "Parsed result": "Parsed result", "Parsed result - Tooltip": "Parsed result - <PERSON><PERSON><PERSON>", "Token type": "Token type", "Token type - Tooltip": "Token type - Tooltip"}, "transaction": {"Amount": "Amount", "Amount - Tooltip": "The amount of traded products", "Edit Transaction": "Edit Transaction", "New Transaction": "New Transaction", "Tag - Tooltip": "The tag of the transaction"}, "user": {"3rd-party logins": "3rd-party logins", "3rd-party logins - Tooltip": "Social logins linked by the user", "Address": "Address", "Address - Tooltip": "Residential address", "Address line": "Address line", "Affiliation": "Affiliation", "Affiliation - Tooltip": "Employer, such as company name or organization name", "Balance": "Balance", "Balance - Tooltip": "User's balance", "Bio": "Bio", "Bio - Tooltip": "Self introduction of the user", "Birthday": "Birthday", "Birthday - Tooltip": "Birthday - <PERSON><PERSON><PERSON>", "Captcha Verify Failed": "Captcha Verify Failed", "Captcha Verify Success": "<PERSON><PERSON>fy <PERSON>", "Country code": "Country code", "Country/Region": "Country/Region", "Country/Region - Tooltip": "Country or region", "Edit User": "Edit User", "Education": "Education", "Education - Tooltip": "Education - Tooltip", "Email cannot be empty": "Email cannot be empty", "Email/phone reset successfully": "Email/phone reset successfully", "Empty input!": "Empty input!", "Face ID": "Face ID", "Face IDs": "Face IDs", "Gender": "Gender", "Gender - Tooltip": "Gender - Tooltip", "Homepage": "Homepage", "Homepage - Tooltip": "Homepage URL of the user", "ID card": "ID card", "ID card - Tooltip": "ID card - Tooltip", "ID card back": "ID card back", "ID card front": "ID card front", "ID card info": "ID card info", "ID card info - Tooltip": "ID card info - Tooltip", "ID card type": "ID card type", "ID card type - Tooltip": "ID card type - Tooltip", "ID card with person": "ID card with person", "Input your email": "Input your email", "Input your phone number": "Input your phone number", "Is admin": "Is admin", "Is admin - Tooltip": "Is an administrator of the organization the user belongs to", "Is deleted": "Is deleted", "Is deleted - Tooltip": "Soft-deleted users only retain database records and cannot perform any operations", "Is forbidden": "Is forbidden", "Is forbidden - Tooltip": "Forbidden users cannot log in any more", "Is online": "Is online", "Karma": "<PERSON>rma", "Karma - Tooltip": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "Keys": "Keys", "Language": "Language", "Language - Tooltip": "Language - Tooltip", "Last change password time": "Last change password time", "Link": "Link", "Location": "Location", "Location - Tooltip": "City of residence", "MFA accounts": "MFA accounts", "Managed accounts": "Managed accounts", "Modify password...": "Modify password...", "Multi-factor authentication": "Multi-factor authentication", "Need update password": "Need update password", "Need update password - Tooltip": "Force user update password after login", "New Email": "New Email", "New Password": "New Password", "New User": "New User", "New phone": "New phone", "Old Password": "Old Password", "Password set successfully": "Password set successfully", "Phone cannot be empty": "Phone cannot be empty", "Please select avatar from resources": "Please select avatar from resources", "Properties": "Properties", "Properties - Tooltip": "Properties of the user", "Ranking": "Ranking", "Ranking - Tooltip": "Ranking - <PERSON><PERSON><PERSON>", "Re-enter New": "Re-enter New", "Reset Email...": "Reset Email...", "Reset Phone...": "Reset Phone...", "Score": "Score", "Score - Tooltip": "Score - <PERSON>lt<PERSON>", "Select a photo...": "Select a photo...", "Set Password": "Set Password", "Set new profile picture": "Set new profile picture", "Set password...": "Set password...", "Tag": "Tag", "Tag - Tooltip": "Tag of the user", "The password must contain at least one special character": "The password must contain at least one special character", "The password must contain at least one uppercase letter, one lowercase letter and one digit": "The password must contain at least one uppercase letter, one lowercase letter and one digit", "The password must have at least 6 characters": "The password must have at least 6 characters", "The password must have at least 8 characters": "The password must have at least 8 characters", "The password must not contain any repeated characters": "The password must not contain any repeated characters", "This field value doesn't match the pattern rule": "This field value doesn't match the pattern rule", "Title": "Title", "Title - Tooltip": "Position in the affiliation", "Two passwords you typed do not match.": "Two passwords you typed do not match.", "Unlink": "Unlink", "Upload (.xlsx)": "Upload (.xlsx)", "Upload ID card back picture": "Upload ID card back picture", "Upload ID card front picture": "Upload ID card front picture", "Upload ID card with person picture": "Upload ID card with person picture", "Upload a photo": "Upload a photo", "User Profile": "User Profile", "Values": "Values", "Verification code sent": "Verification code sent", "WebAuthn credentials": "WebAuthn credentials", "You have changed the username, please save your change first before modifying the password": "You have changed the username, please save your change first before modifying the password", "input password": "input password"}, "verification": {"Is used": "Is used", "Receiver": "Receiver"}, "webhook": {"Content type": "Content type", "Content type - Tooltip": "Content type", "Edit Webhook": "Edit Webhook", "Events": "Events", "Events - Tooltip": "Events", "Extended user fields": "Extended user fields", "Extended user fields - Tooltip": "Extended user fields - Tooltip", "Headers": "Headers", "Headers - Tooltip": "HTTP headers (key-value pairs)", "Is user extended": "Is user extended", "Is user extended - Tooltip": "Whether to include the user's extended fields in the JSON", "Method - Tooltip": "HTTP method", "New Webhook": "New Webhook", "Object fields": "Object fields", "Object fields - Tooltip": "Displayable object fields", "Single org only": "Single org only", "Single org only - Tooltip": "Triggered only in the organization that the webhook belongs to", "Value": "Value"}}