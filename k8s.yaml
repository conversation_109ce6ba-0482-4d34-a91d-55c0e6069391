# this is only an EXAMPLE of deploying casddor in kubernetes
# please modify this file according to your requirements
apiVersion: v1
kind: Service
metadata:
  #EDIT IT: if you don't want to run casdoor in default namespace, please modify this field
  #namespace: casdoor
  name: casdoor-svc
  labels:
    app: casdoor
spec:
  #EDIT IT: if you don't want to run casdoor in default namespace, please modify this filed
  type: NodePort
  ports:
    - port: 8000
  selector:
    app: casdoor
---
apiVersion: apps/v1
kind: Deployment
metadata:
  #EDIT IT: if you don't want to run casdoor in default namespace, please modify this field
  #namespace: casdoor
  name: casdoor-deployment
  labels:
    app: casdoor
spec:
  #EDIT IT: if you don't use redis, casdoor should not have multiple replicas
  replicas: 1
  selector:
    matchLabels:
      app: casdoor
  template:
    metadata:
      labels:
        app: casdoor
    spec:
      containers:
        - name: casdoor-container
          image: casbin/casdoor:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8000
          volumeMounts:
            # the mounted directory path in THE CONTAINER
            - mountPath: /conf
              name: conf
          env:       
            - name: RUNNING_IN_DOCKER
              value: "true"
      #if you want to deploy this in real prod env, consider the config map
      volumes:
        - name: conf
          hostPath:
            #EDIT IT: the mounted directory path in THE HOST
            path: /conf
