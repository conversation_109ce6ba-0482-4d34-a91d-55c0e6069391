linters:
  disable-all: true
  enable:
    - deadcode
    - dupl
    - errcheck
    - goconst
    - gocyclo
    - gofmt
    - goimports
    - gosec
    - gosimple
    - govet
    - ineffassign
    - lll
    - misspell
    - nakedret
    - prealloc
    - staticcheck
    - structcheck
    - typecheck
    - unconvert
    - unparam
    - unused
    - varcheck
    - revive
    - exportloopref
run:
  deadline: 5m
  skip-dirs:
    - api
  # skip-files:
  #   - ".*_test\\.go$"
  modules-download-mode: mod
# all available settings of specific linters
linters-settings:
  lll:
    # max line length, lines longer will be reported. Default is 120.
    # '\t' is counted as 1 character by default, and can be changed with the tab-width option
    line-length: 150
    # tab width in spaces. Default to 1.
    tab-width: 1