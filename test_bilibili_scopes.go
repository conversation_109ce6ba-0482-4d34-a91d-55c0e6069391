package main

import (
	"fmt"
	"net/http"
	"time"

	"github.com/casdoor/casdoor/idp"
)

func main() {
	// 创建 bilibili 提供商实例
	provider := idp.NewBilibiliIdProvider("test_client_id", "test_client_secret", "http://localhost:8000/callback")
	provider.SetHttpClient(&http.Client{Timeout: 30 * time.Second})

	// 测试签名生成
	headers, err := provider.generateBilibiliSignature("test_access_token", "")
	if err != nil {
		fmt.Printf("签名生成失败: %v\n", err)
		return
	}

	fmt.Println("生成的请求头:")
	for key, value := range headers {
		fmt.Printf("%s: %s\n", key, value)
	}

	fmt.Println("\n签名算法测试完成！")
}
