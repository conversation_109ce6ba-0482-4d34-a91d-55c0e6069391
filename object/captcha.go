// Copyright 2021 The Casdoor Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package object

import (
	"bytes"

	"github.com/dchest/captcha"
)

func GetCaptcha() (string, []byte, error) {
	id := captcha.NewLen(5)

	var buffer bytes.Buffer

	err := captcha.WriteImage(&buffer, id, 200, 80)
	if err != nil {
		return "", nil, err
	}

	return id, buffer.Bytes(), nil
}

func VerifyCaptcha(id string, digits string) bool {
	res := captcha.VerifyString(id, digits)

	return res
}
