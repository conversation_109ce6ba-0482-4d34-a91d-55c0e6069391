// Copyright 2023 The Casdoor Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package object

import (
	"strings"
	"time"

	"github.com/casdoor/casdoor/util"
)

func GetDirectResources(owner string, user string, provider *Provider, prefix string, lang string) ([]*Resource, error) {
	storageProvider, err := getStorageProvider(provider, lang)
	if err != nil {
		return nil, err
	}

	res := []*Resource{}
	fullPathPrefix := util.UrlJoin(provider.PathPrefix, prefix)
	objects, err := storageProvider.List(fullPathPrefix)
	for _, obj := range objects {
		resource := &Resource{
			Owner:       owner,
			Name:        strings.TrimPrefix(obj.Path, "/"),
			CreatedTime: obj.LastModified.Local().Format(time.RFC3339),
			User:        user,
			Provider:    "",
			Application: "",
			FileSize:    int(obj.Size),
			Url:         util.UrlJoin(provider.Domain, obj.Path),
		}
		res = append(res, resource)
	}
	return res, err
}
