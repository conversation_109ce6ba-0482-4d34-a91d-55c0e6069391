// Copyright 2023 The Casdoor Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package object

import (
	"strings"

	"github.com/casvisor/casvisor-go-sdk/casvisorsdk"
)

func getCasvisorApplication() *Application {
	applications, err := GetApplications("admin")
	if err != nil {
		panic(err)
	}

	for _, application := range applications {
		if strings.Contains(strings.ToLower(application.Name), "casvisor-my") {
			return application
		}
	}
	return nil
}

func InitCasvisorConfig() {
	application := getCasvisorApplication()
	if application == nil {
		return
	}

	casvisorEndpoint := application.HomepageUrl
	clientId := application.ClientId
	clientSecret := application.ClientSecret
	casdoorOrganization := application.Organization
	casdoorApplication := application.Name

	casvisorsdk.InitConfig(casvisorEndpoint, clientId, clientSecret, casdoorOrganization, casdoorApplication)
}
