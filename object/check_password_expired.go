// Copyright 2024 The Casdoor Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package object

import (
	"fmt"
	"time"

	"github.com/casdoor/casdoor/i18n"
	"github.com/casdoor/casdoor/util"
)

func checkPasswordExpired(user *User, lang string) error {
	organization, err := GetOrganizationByUser(user)
	if err != nil {
		return err
	}
	if organization == nil {
		return fmt.Errorf(i18n.Translate(lang, "check:Organization does not exist"))
	}

	passwordExpireDays := organization.PasswordExpireDays
	if passwordExpireDays <= 0 {
		return nil
	}

	lastChangePasswordTime := user.LastChangePasswordTime
	if lastChangePasswordTime == "" {
		if user.CreatedTime == "" {
			return fmt.Errorf(i18n.Translate(lang, "check:Your password has expired. Please reset your password by clicking \"Forgot password\""))
		}
		lastChangePasswordTime = user.CreatedTime
	}

	lastTime := util.String2Time(lastChangePasswordTime)
	expireTime := lastTime.AddDate(0, 0, passwordExpireDays)
	if time.Now().After(expireTime) {
		return fmt.Errorf(i18n.Translate(lang, "check:Your password has expired. Please reset your password by clicking \"Forgot password\""))
	}
	return nil
}
