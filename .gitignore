# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
*.swp

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/
bin/

.idea/
*.iml
.vscode/settings.json

tmp/
tmpFiles/
*.tmp
logs/
files/
lastupdate.tmp
commentsRouter*.go

# ignore build result
casdoor
server

# include helm-chart
!manifests/casdoor
