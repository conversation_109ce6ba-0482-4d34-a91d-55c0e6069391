swagger: "2.0"
info:
  title: Casdoor RESTful API
  description: Swagger Docs of Casdoor Backend API
  version: 1.503.0
  contact:
    email: <EMAIL>
basePath: /
schemes:
- https
- http
paths:
  /.well-known/jwks:
    get:
      tags:
      - OIDC API
      operationId: RootController.GetJwks
      responses:
        "200":
          description: ""
          schema:
            $ref: '#/definitions/jose.JSONWebKey'
  /.well-known/openid-configuration:
    get:
      tags:
      - OIDC API
      description: Get Oidc Discovery
      operationId: RootController.GetOidcDiscovery
      responses:
        "200":
          description: ""
          schema:
            $ref: '#/definitions/object.OidcDiscovery'
  /api/Callback:
    post:
      tags:
      - Callback API
      description: Get Login Error Counts
      operationId: ApiController.Callback
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Userinfo'
  /api/add-adapter:
    post:
      tags:
      - Adapter API
      description: add adapter
      operationId: ApiController.AddAdapter
      parameters:
      - in: body
        name: body
        description: The details of the adapter
        required: true
        schema:
          $ref: '#/definitions/object.Adapter'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-application:
    post:
      tags:
      - Application API
      description: add an application
      operationId: ApiController.AddApplication
      parameters:
      - in: body
        name: body
        description: The details of the application
        required: true
        schema:
          $ref: '#/definitions/object.Application'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-cert:
    post:
      tags:
      - Cert API
      description: add cert
      operationId: ApiController.AddCert
      parameters:
      - in: body
        name: body
        description: The details of the cert
        required: true
        schema:
          $ref: '#/definitions/object.Cert'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-enforcer:
    post:
      tags:
      - Enforcer API
      description: add enforcer
      operationId: ApiController.AddEnforcer
      parameters:
      - in: body
        name: enforcer
        description: The enforcer object
        required: true
        schema:
          $ref: '#/definitions/object'
      responses:
        "200":
          description: ""
          schema:
            $ref: '#/definitions/object.Enforcer'
  /api/add-group:
    post:
      tags:
      - Group API
      description: add group
      operationId: ApiController.AddGroup
      parameters:
      - in: body
        name: body
        description: The details of the group
        required: true
        schema:
          $ref: '#/definitions/object.Group'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-invitation:
    post:
      tags:
      - Invitation API
      description: add invitation
      operationId: ApiController.AddInvitation
      parameters:
      - in: body
        name: body
        description: The details of the invitation
        required: true
        schema:
          $ref: '#/definitions/object.Invitation'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-ldap:
    post:
      tags:
      - Account API
      description: add ldap
      operationId: ApiController.AddLdap
      parameters:
      - in: body
        name: body
        description: The details of the ldap
        required: true
        schema:
          $ref: '#/definitions/object.Ldap'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-model:
    post:
      tags:
      - Model API
      description: add model
      operationId: ApiController.AddModel
      parameters:
      - in: body
        name: body
        description: The details of the model
        required: true
        schema:
          $ref: '#/definitions/object.Model'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-organization:
    post:
      tags:
      - Organization API
      description: add organization
      operationId: ApiController.AddOrganization
      parameters:
      - in: body
        name: body
        description: The details of the organization
        required: true
        schema:
          $ref: '#/definitions/object.Organization'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-payment:
    post:
      tags:
      - Payment API
      description: add payment
      operationId: ApiController.AddPayment
      parameters:
      - in: body
        name: body
        description: The details of the payment
        required: true
        schema:
          $ref: '#/definitions/object.Payment'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-permission:
    post:
      tags:
      - Permission API
      description: add permission
      operationId: ApiController.AddPermission
      parameters:
      - in: body
        name: body
        description: The details of the permission
        required: true
        schema:
          $ref: '#/definitions/object.Permission'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-plan:
    post:
      tags:
      - Plan API
      description: add plan
      operationId: ApiController.AddPlan
      parameters:
      - in: body
        name: body
        description: The details of the plan
        required: true
        schema:
          $ref: '#/definitions/object.Plan'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-pricing:
    post:
      tags:
      - Pricing API
      description: add pricing
      operationId: ApiController.AddPricing
      parameters:
      - in: body
        name: body
        description: The details of the pricing
        required: true
        schema:
          $ref: '#/definitions/object.Pricing'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-product:
    post:
      tags:
      - Product API
      description: add product
      operationId: ApiController.AddProduct
      parameters:
      - in: body
        name: body
        description: The details of the product
        required: true
        schema:
          $ref: '#/definitions/object.Product'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-provider:
    post:
      tags:
      - Provider API
      description: add provider
      operationId: ApiController.AddProvider
      parameters:
      - in: body
        name: body
        description: The details of the provider
        required: true
        schema:
          $ref: '#/definitions/object.Provider'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-resource:
    post:
      tags:
      - Resource API
      operationId: ApiController.AddResource
      parameters:
      - in: body
        name: resource
        description: Resource object
        required: true
        schema:
          $ref: '#/definitions/object.Resource'
      responses:
        "200":
          description: Success or error
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-role:
    post:
      tags:
      - Role API
      description: add role
      operationId: ApiController.AddRole
      parameters:
      - in: body
        name: body
        description: The details of the role
        required: true
        schema:
          $ref: '#/definitions/object.Role'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-session:
    post:
      tags:
      - Session API
      description: Add session for one user in one application. If there are other existing sessions, join the session into the list.
      operationId: ApiController.AddSession
      parameters:
      - in: query
        name: id
        description: The id(organization/application/user) of session
        required: true
        type: string
      - in: query
        name: sessionId
        description: sessionId to be added
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              type: string
  /api/add-subscription:
    post:
      tags:
      - Subscription API
      description: add subscription
      operationId: ApiController.AddSubscription
      parameters:
      - in: body
        name: body
        description: The details of the subscription
        required: true
        schema:
          $ref: '#/definitions/object.Subscription'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-syncer:
    post:
      tags:
      - Syncer API
      description: add syncer
      operationId: ApiController.AddSyncer
      parameters:
      - in: body
        name: body
        description: The details of the syncer
        required: true
        schema:
          $ref: '#/definitions/object.Syncer'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-token:
    post:
      tags:
      - Token API
      description: add token
      operationId: ApiController.AddToken
      parameters:
      - in: body
        name: body
        description: Details of the token
        required: true
        schema:
          $ref: '#/definitions/object.Token'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-user:
    post:
      tags:
      - User API
      description: add user
      operationId: ApiController.AddUser
      parameters:
      - in: body
        name: body
        description: The details of the user
        required: true
        schema:
          $ref: '#/definitions/object.User'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/add-user-keys:
    post:
      tags:
      - User API
      operationId: ApiController.AddUserKeys
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Userinfo'
  /api/add-webhook:
    post:
      tags:
      - Webhook API
      description: add webhook
      operationId: ApiController.AddWebhook
      parameters:
      - in: body
        name: body
        description: The details of the webhook
        required: true
        schema:
          $ref: '#/definitions/object.Webhook'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/batch-enforce:
    post:
      tags:
      - Enforcer API
      description: Call Casbin BatchEnforce API
      operationId: ApiController.BatchEnforce
      parameters:
      - in: body
        name: body
        description: array of casbin requests
        required: true
        schema:
          type: array
          items:
            type: string
      - in: query
        name: permissionId
        description: permission id
        type: string
      - in: query
        name: modelId
        description: model id
        type: string
      - in: query
        name: owner
        description: owner
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/buy-product:
    post:
      tags:
      - Product API
      description: buy product
      operationId: ApiController.BuyProduct
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the product
        required: true
        type: string
      - in: query
        name: providerName
        description: The name of the provider
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/check-user-password:
    post:
      tags:
      - User API
      operationId: ApiController.CheckUserPassword
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Userinfo'
  /api/delete-adapter:
    post:
      tags:
      - Adapter API
      description: delete adapter
      operationId: ApiController.DeleteAdapter
      parameters:
      - in: body
        name: body
        description: The details of the adapter
        required: true
        schema:
          $ref: '#/definitions/object.Adapter'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-application:
    post:
      tags:
      - Application API
      description: delete an application
      operationId: ApiController.DeleteApplication
      parameters:
      - in: body
        name: body
        description: The details of the application
        required: true
        schema:
          $ref: '#/definitions/object.Application'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-cert:
    post:
      tags:
      - Cert API
      description: delete cert
      operationId: ApiController.DeleteCert
      parameters:
      - in: body
        name: body
        description: The details of the cert
        required: true
        schema:
          $ref: '#/definitions/object.Cert'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-enforcer:
    post:
      tags:
      - Enforcer API
      description: delete enforcer
      operationId: ApiController.DeleteEnforcer
      parameters:
      - in: body
        name: body
        description: The enforcer object
        required: true
        schema:
          $ref: '#/definitions/object.Enforcer'
      responses:
        "200":
          description: ""
          schema:
            $ref: '#/definitions/object.Enforcer'
  /api/delete-group:
    post:
      tags:
      - Group API
      description: delete group
      operationId: ApiController.DeleteGroup
      parameters:
      - in: body
        name: body
        description: The details of the group
        required: true
        schema:
          $ref: '#/definitions/object.Group'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-invitation:
    post:
      tags:
      - Invitation API
      description: delete invitation
      operationId: ApiController.DeleteInvitation
      parameters:
      - in: body
        name: body
        description: The details of the invitation
        required: true
        schema:
          $ref: '#/definitions/object.Invitation'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-ldap:
    post:
      tags:
      - Account API
      description: delete ldap
      operationId: ApiController.DeleteLdap
      parameters:
      - in: body
        name: body
        description: The details of the ldap
        required: true
        schema:
          $ref: '#/definitions/object.Ldap'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-mfa/:
    post:
      tags:
      - MFA API
      description: ': Delete MFA'
      operationId: ApiController.DeleteMfa
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-model:
    post:
      tags:
      - Model API
      description: delete model
      operationId: ApiController.DeleteModel
      parameters:
      - in: body
        name: body
        description: The details of the model
        required: true
        schema:
          $ref: '#/definitions/object.Model'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-organization:
    post:
      tags:
      - Organization API
      description: delete organization
      operationId: ApiController.DeleteOrganization
      parameters:
      - in: body
        name: body
        description: The details of the organization
        required: true
        schema:
          $ref: '#/definitions/object.Organization'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-payment:
    post:
      tags:
      - Payment API
      description: delete payment
      operationId: ApiController.DeletePayment
      parameters:
      - in: body
        name: body
        description: The details of the payment
        required: true
        schema:
          $ref: '#/definitions/object.Payment'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-permission:
    post:
      tags:
      - Permission API
      description: delete permission
      operationId: ApiController.DeletePermission
      parameters:
      - in: body
        name: body
        description: The details of the permission
        required: true
        schema:
          $ref: '#/definitions/object.Permission'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-plan:
    post:
      tags:
      - Plan API
      description: delete plan
      operationId: ApiController.DeletePlan
      parameters:
      - in: body
        name: body
        description: The details of the plan
        required: true
        schema:
          $ref: '#/definitions/object.Plan'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-pricing:
    post:
      tags:
      - Pricing API
      description: delete pricing
      operationId: ApiController.DeletePricing
      parameters:
      - in: body
        name: body
        description: The details of the pricing
        required: true
        schema:
          $ref: '#/definitions/object.Pricing'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-product:
    post:
      tags:
      - Product API
      description: delete product
      operationId: ApiController.DeleteProduct
      parameters:
      - in: body
        name: body
        description: The details of the product
        required: true
        schema:
          $ref: '#/definitions/object.Product'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-provider:
    post:
      tags:
      - Provider API
      description: delete provider
      operationId: ApiController.DeleteProvider
      parameters:
      - in: body
        name: body
        description: The details of the provider
        required: true
        schema:
          $ref: '#/definitions/object.Provider'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-resource:
    post:
      tags:
      - Resource API
      operationId: ApiController.DeleteResource
      parameters:
      - in: body
        name: resource
        description: Resource object
        required: true
        schema:
          $ref: '#/definitions/object.Resource'
      responses:
        "200":
          description: Success or error
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-role:
    post:
      tags:
      - Role API
      description: delete role
      operationId: ApiController.DeleteRole
      parameters:
      - in: body
        name: body
        description: The details of the role
        required: true
        schema:
          $ref: '#/definitions/object.Role'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-session:
    post:
      tags:
      - Session API
      description: Delete session for one user in one application.
      operationId: ApiController.DeleteSession
      parameters:
      - in: query
        name: id
        description: The id(organization/application/user) of session
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              type: string
  /api/delete-subscription:
    post:
      tags:
      - Subscription API
      description: delete subscription
      operationId: ApiController.DeleteSubscription
      parameters:
      - in: body
        name: body
        description: The details of the subscription
        required: true
        schema:
          $ref: '#/definitions/object.Subscription'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-syncer:
    post:
      tags:
      - Syncer API
      description: delete syncer
      operationId: ApiController.DeleteSyncer
      parameters:
      - in: body
        name: body
        description: The details of the syncer
        required: true
        schema:
          $ref: '#/definitions/object.Syncer'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-token:
    post:
      tags:
      - Token API
      description: delete token
      operationId: ApiController.DeleteToken
      parameters:
      - in: body
        name: body
        description: Details of the token
        required: true
        schema:
          $ref: '#/definitions/object.Token'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-user:
    post:
      tags:
      - User API
      description: delete user
      operationId: ApiController.DeleteUser
      parameters:
      - in: body
        name: body
        description: The details of the user
        required: true
        schema:
          $ref: '#/definitions/object.User'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/delete-webhook:
    post:
      tags:
      - Webhook API
      description: delete webhook
      operationId: ApiController.DeleteWebhook
      parameters:
      - in: body
        name: body
        description: The details of the webhook
        required: true
        schema:
          $ref: '#/definitions/object.Webhook'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/enforce:
    post:
      tags:
      - Enforcer API
      description: Call Casbin Enforce API
      operationId: ApiController.Enforce
      parameters:
      - in: body
        name: body
        description: Casbin request
        required: true
        schema:
          type: array
          items:
            type: string
      - in: query
        name: permissionId
        description: permission id
        type: string
      - in: query
        name: modelId
        description: model id
        type: string
      - in: query
        name: resourceId
        description: resource id
        type: string
      - in: query
        name: owner
        description: owner
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/get-account:
    get:
      tags:
      - Account API
      description: get the details of the current account
      operationId: ApiController.GetAccount
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/get-adapter:
    get:
      tags:
      - Adapter API
      description: get adapter
      operationId: ApiController.GetAdapter
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the adapter
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Adapter'
  /api/get-adapters:
    get:
      tags:
      - Adapter API
      description: get adapters
      operationId: ApiController.GetAdapters
      parameters:
      - in: query
        name: owner
        description: The owner of adapters
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Adapter'
  /api/get-app-login:
    get:
      tags:
      - Login API
      description: get application login
      operationId: ApiController.GetApplicationLogin
      parameters:
      - in: query
        name: clientId
        description: client id
        required: true
        type: string
      - in: query
        name: responseType
        description: response type
        required: true
        type: string
      - in: query
        name: redirectUri
        description: redirect uri
        required: true
        type: string
      - in: query
        name: scope
        description: scope
        required: true
        type: string
      - in: query
        name: state
        description: state
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/get-application:
    get:
      tags:
      - Application API
      description: get the detail of an application
      operationId: ApiController.GetApplication
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the application.
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Application'
  /api/get-applications:
    get:
      tags:
      - Application API
      description: get all applications
      operationId: ApiController.GetApplications
      parameters:
      - in: query
        name: owner
        description: The owner of applications.
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Application'
  /api/get-captcha:
    get:
      tags:
      - Login API
      operationId: ApiController.GetCaptcha
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Userinfo'
  /api/get-captcha-status:
    get:
      tags:
      - Token API
      description: Get Login Error Counts
      operationId: ApiController.GetCaptchaStatus
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of user
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/get-cert:
    get:
      tags:
      - Cert API
      description: get cert
      operationId: ApiController.GetCert
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the cert
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Cert'
  /api/get-certs:
    get:
      tags:
      - Cert API
      description: get certs
      operationId: ApiController.GetCerts
      parameters:
      - in: query
        name: owner
        description: The owner of certs
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Cert'
  /api/get-dashboard:
    get:
      tags:
      - System API
      description: get information of dashboard
      operationId: ApiController.GetDashboard
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/get-default-application:
    get:
      tags:
      - Organization API
      description: get default application
      operationId: ApiController.GetDefaultApplication
      parameters:
      - in: query
        name: id
        description: organization id
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/get-email-and-phone:
    get:
      tags:
      - User API
      description: get email and phone by username
      operationId: ApiController.GetEmailAndPhone
      parameters:
      - in: formData
        name: username
        description: The username of the user
        required: true
        type: string
      - in: formData
        name: organization
        description: The organization of the user
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/get-enforcer:
    get:
      tags:
      - Enforcer API
      description: get enforcer
      operationId: ApiController.GetEnforcer
      parameters:
      - in: query
        name: id
        description: The id ( owner/name )  of enforcer
        required: true
        type: string
      responses:
        "200":
          description: ""
          schema:
            $ref: '#/definitions/object.Enforcer'
  /api/get-enforcers:
    get:
      tags:
      - Enforcer API
      description: get enforcers
      operationId: ApiController.GetEnforcers
      parameters:
      - in: query
        name: owner
        description: The owner of enforcers
        required: true
        type: string
      responses:
        "200":
          description: ""
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Enforcer'
  /api/get-global-certs:
    get:
      tags:
      - Cert API
      description: get globle certs
      operationId: ApiController.GetGlobalCerts
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Cert'
  /api/get-global-providers:
    get:
      tags:
      - Provider API
      description: get Global providers
      operationId: ApiController.GetGlobalProviders
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Provider'
  /api/get-global-users:
    get:
      tags:
      - User API
      description: get global users
      operationId: ApiController.GetGlobalUsers
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.User'
  /api/get-group:
    get:
      tags:
      - Group API
      description: get group
      operationId: ApiController.GetGroup
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the group
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Group'
  /api/get-groups:
    get:
      tags:
      - Group API
      description: get groups
      operationId: ApiController.GetGroups
      parameters:
      - in: query
        name: owner
        description: The owner of groups
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Group'
  /api/get-invitation:
    get:
      tags:
      - Invitation API
      description: get invitation
      operationId: ApiController.GetInvitation
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the invitation
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Invitation'
  /api/get-invitations:
    get:
      tags:
      - Invitation API
      description: get invitations
      operationId: ApiController.GetInvitations
      parameters:
      - in: query
        name: owner
        description: The owner of invitations
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Invitation'
  /api/get-ldap:
    get:
      tags:
      - Account API
      description: get ldap
      operationId: ApiController.GetLdap
      parameters:
      - in: query
        name: id
        description: id
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Ldap'
  /api/get-ldap-users:
    get:
      tags:
      - Account API
      description: get ldap users
      operationId: ApiController.GetLdapser
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.LdapResp'
  /api/get-ldaps:
    get:
      tags:
      - Account API
      description: get ldaps
      operationId: ApiController.GetLdaps
      parameters:
      - in: query
        name: owner
        description: owner
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Ldap'
  /api/get-model:
    get:
      tags:
      - Model API
      description: get model
      operationId: ApiController.GetModel
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the model
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Model'
  /api/get-models:
    get:
      tags:
      - Model API
      description: get models
      operationId: ApiController.GetModels
      parameters:
      - in: query
        name: owner
        description: The owner of models
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Model'
  /api/get-organization:
    get:
      tags:
      - Organization API
      description: get organization
      operationId: ApiController.GetOrganization
      parameters:
      - in: query
        name: id
        description: organization id
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Organization'
  /api/get-organization-applications:
    get:
      tags:
      - Application API
      description: get the detail of the organization's application
      operationId: ApiController.GetOrganizationApplications
      parameters:
      - in: query
        name: organization
        description: The organization name
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Application'
  /api/get-organization-names:
    get:
      tags:
      - Organization API
      description: get all organization name and displayName
      operationId: ApiController.GetOrganizationNames
      parameters:
      - in: query
        name: owner
        description: owner
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Organization'
  /api/get-organizations:
    get:
      tags:
      - Organization API
      description: get organizations
      operationId: ApiController.GetOrganizations
      parameters:
      - in: query
        name: owner
        description: owner
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Organization'
  /api/get-payment:
    get:
      tags:
      - Payment API
      description: get payment
      operationId: ApiController.GetPayment
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the payment
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Payment'
  /api/get-payments:
    get:
      tags:
      - Payment API
      description: get payments
      operationId: ApiController.GetPayments
      parameters:
      - in: query
        name: owner
        description: The owner of payments
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Payment'
  /api/get-permission:
    get:
      tags:
      - Permission API
      description: get permission
      operationId: ApiController.GetPermission
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the permission
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Permission'
  /api/get-permissions:
    get:
      tags:
      - Permission API
      description: get permissions
      operationId: ApiController.GetPermissions
      parameters:
      - in: query
        name: owner
        description: The owner of permissions
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Permission'
  /api/get-permissions-by-role:
    get:
      tags:
      - Permission API
      description: get permissions by role
      operationId: ApiController.GetPermissionsByRole
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the role
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Permission'
  /api/get-permissions-by-submitter:
    get:
      tags:
      - Permission API
      description: get permissions by submitter
      operationId: ApiController.GetPermissionsBySubmitter
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Permission'
  /api/get-plan:
    get:
      tags:
      - Plan API
      description: get plan
      operationId: ApiController.GetPlan
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the plan
        required: true
        type: string
      - in: query
        name: includeOption
        description: Should include plan's option
        type: boolean
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Plan'
  /api/get-plans:
    get:
      tags:
      - Plan API
      description: get plans
      operationId: ApiController.GetPlans
      parameters:
      - in: query
        name: owner
        description: The owner of plans
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Plan'
  /api/get-pricing:
    get:
      tags:
      - Pricing API
      description: get pricing
      operationId: ApiController.GetPricing
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the pricing
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Pricing'
  /api/get-pricings:
    get:
      tags:
      - Pricing API
      description: get pricings
      operationId: ApiController.GetPricings
      parameters:
      - in: query
        name: owner
        description: The owner of pricings
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Pricing'
  /api/get-product:
    get:
      tags:
      - Product API
      description: get product
      operationId: ApiController.GetProduct
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the product
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Product'
  /api/get-products:
    get:
      tags:
      - Product API
      description: get products
      operationId: ApiController.GetProducts
      parameters:
      - in: query
        name: owner
        description: The owner of products
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Product'
  /api/get-prometheus-info:
    get:
      tags:
      - System API
      description: get Prometheus Info
      operationId: ApiController.GetPrometheusInfo
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.PrometheusInfo'
  /api/get-provider:
    get:
      tags:
      - Provider API
      description: get provider
      operationId: ApiController.GetProvider
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the provider
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Provider'
  /api/get-providers:
    get:
      tags:
      - Provider API
      description: get providers
      operationId: ApiController.GetProviders
      parameters:
      - in: query
        name: owner
        description: The owner of providers
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Provider'
  /api/get-resource:
    get:
      tags:
      - Resource API
      description: get resource
      operationId: ApiController.GetResource
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of resource
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Resource'
  /api/get-resources:
    get:
      tags:
      - Resource API
      description: get resources
      operationId: ApiController.GetResources
      parameters:
      - in: query
        name: owner
        description: Owner
        required: true
        type: string
      - in: query
        name: user
        description: User
        required: true
        type: string
      - in: query
        name: pageSize
        description: Page Size
        type: integer
      - in: query
        name: p
        description: Page Number
        type: integer
      - in: query
        name: field
        description: Field
        type: string
      - in: query
        name: value
        description: Value
        type: string
      - in: query
        name: sortField
        description: Sort Field
        type: string
      - in: query
        name: sortOrder
        description: Sort Order
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Resource'
  /api/get-role:
    get:
      tags:
      - Role API
      description: get role
      operationId: ApiController.GetRole
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the role
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Role'
  /api/get-roles:
    get:
      tags:
      - Role API
      description: get roles
      operationId: ApiController.GetRoles
      parameters:
      - in: query
        name: owner
        description: The owner of roles
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Role'
  /api/get-session:
    get:
      tags:
      - Session API
      description: Get session for one user in one application.
      operationId: ApiController.GetSingleSession
      parameters:
      - in: query
        name: id
        description: The id(organization/application/user) of session
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              type: string
  /api/get-sessions:
    get:
      tags:
      - Session API
      description: Get organization user sessions.
      operationId: ApiController.GetSessions
      parameters:
      - in: query
        name: owner
        description: The organization name
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              type: string
  /api/get-sorted-users:
    get:
      tags:
      - User API
      operationId: ApiController.GetSortedUsers
      parameters:
      - in: query
        name: owner
        description: The owner of users
        required: true
        type: string
      - in: query
        name: sorter
        description: The DB column name to sort by, e.g., created_time
        required: true
        type: string
      - in: query
        name: limit
        description: The count of users to return, e.g., 25
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.User'
  /api/get-subscription:
    get:
      tags:
      - Subscription API
      description: get subscription
      operationId: ApiController.GetSubscription
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the subscription
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Subscription'
  /api/get-subscriptions:
    get:
      tags:
      - Subscription API
      description: get subscriptions
      operationId: ApiController.GetSubscriptions
      parameters:
      - in: query
        name: owner
        description: The owner of subscriptions
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Subscription'
  /api/get-syncer:
    get:
      tags:
      - Syncer API
      description: get syncer
      operationId: ApiController.GetSyncer
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the syncer
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Syncer'
  /api/get-syncers:
    get:
      tags:
      - Syncer API
      description: get syncers
      operationId: ApiController.GetSyncers
      parameters:
      - in: query
        name: owner
        description: The owner of syncers
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Syncer'
  /api/get-system-info:
    get:
      tags:
      - System API
      description: get system info like CPU and memory usage
      operationId: ApiController.GetSystemInfo
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/util.SystemInfo'
  /api/get-token:
    get:
      tags:
      - Token API
      description: get token
      operationId: ApiController.GetToken
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of token
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Token'
  /api/get-tokens:
    get:
      tags:
      - Token API
      description: get tokens
      operationId: ApiController.GetTokens
      parameters:
      - in: query
        name: owner
        description: The owner of tokens
        required: true
        type: string
      - in: query
        name: pageSize
        description: The size of each page
        required: true
        type: string
      - in: query
        name: p
        description: The number of the page
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Token'
  /api/get-user:
    get:
      tags:
      - User API
      description: get user
      operationId: ApiController.GetUser
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the user
        type: string
      - in: query
        name: owner
        description: The owner of the user
        type: string
      - in: query
        name: email
        description: The email of the user
        type: string
      - in: query
        name: phone
        description: The phone of the user
        type: string
      - in: query
        name: userId
        description: The userId of the user
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.User'
  /api/get-user-application:
    get:
      tags:
      - Application API
      description: get the detail of the user's application
      operationId: ApiController.GetUserApplication
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the user
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Application'
  /api/get-user-count:
    get:
      tags:
      - User API
      operationId: ApiController.GetUserCount
      parameters:
      - in: query
        name: owner
        description: The owner of users
        required: true
        type: string
      - in: query
        name: isOnline
        description: The filter for query, 1 for online, 0 for offline, empty string for all users
        required: true
        type: string
      responses:
        "200":
          description: '{int} int The count of filtered users for an organization'
  /api/get-user-payments:
    get:
      tags:
      - Payment API
      description: get payments for a user
      operationId: ApiController.GetUserPayments
      parameters:
      - in: query
        name: owner
        description: The owner of payments
        required: true
        type: string
      - in: query
        name: organization
        description: The organization of the user
        required: true
        type: string
      - in: query
        name: user
        description: The username of the user
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Payment'
  /api/get-users:
    get:
      tags:
      - User API
      operationId: ApiController.GetUsers
      parameters:
      - in: query
        name: owner
        description: The owner of users
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.User'
  /api/get-version-info:
    get:
      tags:
      - System API
      description: get version info like Casdoor release version and commit ID
      operationId: ApiController.GetVersionInfo
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/util.VersionInfo'
  /api/get-webhook:
    get:
      tags:
      - Webhook API
      description: get webhook
      operationId: ApiController.GetWebhook
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the webhook
        required: true
        type: string
        default: built-in/admin
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Webhook'
  /api/get-webhook-event:
    get:
      tags:
      - System API
      operationId: ApiController.GetWebhookEventType
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Userinfo'
  /api/get-webhooks:
    get:
      tags:
      - Webhook API
      description: get webhooks
      operationId: ApiController.GetWebhooks
      parameters:
      - in: query
        name: owner
        description: The owner of webhooks
        required: true
        type: string
        default: built-in/admin
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              $ref: '#/definitions/object.Webhook'
      security:
      - test_apiKey: []
  /api/health:
    get:
      tags:
      - System API
      description: check if the system is live
      operationId: ApiController.Health
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/invoice-payment:
    post:
      tags:
      - Payment API
      description: invoice payment
      operationId: ApiController.InvoicePayment
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the payment
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/is-session-duplicated:
    get:
      tags:
      - Session API
      description: Check if there are other different sessions for one user in one application.
      operationId: ApiController.IsSessionDuplicated
      parameters:
      - in: query
        name: id
        description: The id(organization/application/user) of session
        required: true
        type: string
      - in: query
        name: sessionId
        description: sessionId to be checked
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              type: string
  /api/login:
    post:
      tags:
      - Login API
      description: login
      operationId: ApiController.Login
      parameters:
      - in: query
        name: clientId
        description: clientId
        required: true
        type: string
      - in: query
        name: responseType
        description: responseType
        required: true
        type: string
      - in: query
        name: redirectUri
        description: redirectUri
        required: true
        type: string
      - in: query
        name: scope
        description: scope
        type: string
      - in: query
        name: state
        description: state
        type: string
      - in: query
        name: nonce
        description: nonce
        type: string
      - in: query
        name: code_challenge_method
        description: code_challenge_method
        type: string
      - in: query
        name: code_challenge
        description: code_challenge
        type: string
      - in: body
        name: form
        description: Login information
        required: true
        schema:
          $ref: '#/definitions/controllers.AuthForm'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/login/oauth/access_token:
    post:
      tags:
      - Token API
      description: get OAuth access token
      operationId: ApiController.GetOAuthToken
      parameters:
      - in: query
        name: grant_type
        description: OAuth grant type
        required: true
        type: string
      - in: query
        name: client_id
        description: OAuth client id
        required: true
        type: string
      - in: query
        name: client_secret
        description: OAuth client secret
        required: true
        type: string
      - in: query
        name: code
        description: OAuth code
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.TokenWrapper'
        "400":
          description: The Response object
          schema:
            $ref: '#/definitions/object.TokenError'
        "401":
          description: The Response object
          schema:
            $ref: '#/definitions/object.TokenError'
  /api/login/oauth/introspect:
    post:
      tags:
      - Login API
      description: The introspection endpoint is an OAuth 2.0 endpoint that takes a
      operationId: ApiController.IntrospectToken
      parameters:
      - in: formData
        name: token
        description: access_token's value or refresh_token's value
        required: true
        type: string
      - in: formData
        name: token_type_hint
        description: the token type access_token or refresh_token
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.IntrospectionResponse'
        "400":
          description: The Response object
          schema:
            $ref: '#/definitions/object.TokenError'
        "401":
          description: The Response object
          schema:
            $ref: '#/definitions/object.TokenError'
  /api/login/oauth/refresh_token:
    post:
      tags:
      - Token API
      description: refresh OAuth access token
      operationId: ApiController.RefreshToken
      parameters:
      - in: query
        name: grant_type
        description: OAuth grant type
        required: true
        type: string
      - in: query
        name: refresh_token
        description: OAuth refresh token
        required: true
        type: string
      - in: query
        name: scope
        description: OAuth scope
        required: true
        type: string
      - in: query
        name: client_id
        description: OAuth client id
        required: true
        type: string
      - in: query
        name: client_secret
        description: OAuth client secret
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.TokenWrapper'
        "400":
          description: The Response object
          schema:
            $ref: '#/definitions/object.TokenError'
        "401":
          description: The Response object
          schema:
            $ref: '#/definitions/object.TokenError'
  /api/logout:
    post:
      tags:
      - Login API
      description: logout the current user
      operationId: ApiController.Logout
      parameters:
      - in: query
        name: id_token_hint
        description: id_token_hint
        type: string
      - in: query
        name: post_logout_redirect_uri
        description: post_logout_redirect_uri
        type: string
      - in: query
        name: state
        description: state
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/mfa/setup/enable:
    post:
      tags:
      - MFA API
      description: enable totp
      operationId: ApiController.MfaSetupEnable
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/mfa/setup/initiate:
    post:
      tags:
      - MFA API
      description: setup MFA
      operationId: ApiController.MfaSetupInitiate
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/mfa/setup/verify:
    post:
      tags:
      - MFA API
      description: setup verify totp
      operationId: ApiController.MfaSetupVerify
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/notify-payment:
    post:
      tags:
      - Payment API
      description: notify payment
      operationId: ApiController.NotifyPayment
      parameters:
      - in: body
        name: body
        description: The details of the payment
        required: true
        schema:
          $ref: '#/definitions/object.Payment'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/reset-email-or-phone:
    post:
      tags:
      - Account API
      operationId: ApiController.ResetEmailOrPhone
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Userinfo'
  /api/run-syncer:
    get:
      tags:
      - Syncer API
      description: run syncer
      operationId: ApiController.RunSyncer
      parameters:
      - in: body
        name: body
        description: The details of the syncer
        required: true
        schema:
          $ref: '#/definitions/object.Syncer'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/send-email:
    post:
      tags:
      - Service API
      description: This API is not for Casdoor frontend to call, it is for Casdoor SDKs.
      operationId: ApiController.SendEmail
      parameters:
      - in: query
        name: clientId
        description: The clientId of the application
        required: true
        type: string
      - in: query
        name: clientSecret
        description: The clientSecret of the application
        required: true
        type: string
      - in: body
        name: from
        description: Details of the email request
        required: true
        schema:
          $ref: '#/definitions/controllers.EmailForm'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/send-notification:
    post:
      tags:
      - Service API
      description: This API is not for Casdoor frontend to call, it is for Casdoor SDKs.
      operationId: ApiController.SendNotification
      parameters:
      - in: body
        name: from
        description: Details of the notification request
        required: true
        schema:
          $ref: '#/definitions/controllers.NotificationForm'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/send-sms:
    post:
      tags:
      - Service API
      description: This API is not for Casdoor frontend to call, it is for Casdoor SDKs.
      operationId: ApiController.SendSms
      parameters:
      - in: query
        name: clientId
        description: The clientId of the application
        required: true
        type: string
      - in: query
        name: clientSecret
        description: The clientSecret of the application
        required: true
        type: string
      - in: body
        name: from
        description: Details of the sms request
        required: true
        schema:
          $ref: '#/definitions/controllers.SmsForm'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/send-verification-code:
    post:
      tags:
      - Verification API
      operationId: ApiController.SendVerificationCode
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Userinfo'
  /api/set-password:
    post:
      tags:
      - Account API
      description: set password
      operationId: ApiController.SetPassword
      parameters:
      - in: formData
        name: userOwner
        description: The owner of the user
        required: true
        type: string
      - in: formData
        name: userName
        description: The name of the user
        required: true
        type: string
      - in: formData
        name: oldPassword
        description: The old password of the user
        required: true
        type: string
      - in: formData
        name: newPassword
        description: The new password of the user
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/set-preferred-mfa:
    post:
      tags:
      - MFA API
      description: ': Set specific Mfa Preferred'
      operationId: ApiController.SetPreferredMfa
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/signup:
    post:
      tags:
      - Login API
      description: sign up a new user
      operationId: ApiController.Signup
      parameters:
      - in: formData
        name: username
        description: The username to sign up
        required: true
        type: string
      - in: formData
        name: password
        description: The password
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/sync-ldap-users:
    post:
      tags:
      - Account API
      description: sync ldap users
      operationId: ApiController.SyncLdapUsers
      parameters:
      - in: query
        name: id
        description: id
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.LdapSyncResp'
  /api/unlink:
    post:
      tags:
      - Login API
      operationId: ApiController.Unlink
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Userinfo'
  /api/update-adapter:
    post:
      tags:
      - Adapter API
      description: update adapter
      operationId: ApiController.UpdateAdapter
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the adapter
        required: true
        type: string
      - in: body
        name: body
        description: The details of the adapter
        required: true
        schema:
          $ref: '#/definitions/object.Adapter'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-application:
    post:
      tags:
      - Application API
      description: update an application
      operationId: ApiController.UpdateApplication
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the application
        required: true
        type: string
      - in: body
        name: body
        description: The details of the application
        required: true
        schema:
          $ref: '#/definitions/object.Application'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-cert:
    post:
      tags:
      - Cert API
      description: update cert
      operationId: ApiController.UpdateCert
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the cert
        required: true
        type: string
      - in: body
        name: body
        description: The details of the cert
        required: true
        schema:
          $ref: '#/definitions/object.Cert'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-enforcer:
    post:
      tags:
      - Enforcer API
      description: update enforcer
      operationId: ApiController.UpdateEnforcer
      parameters:
      - in: query
        name: id
        description: The id ( owner/name )  of enforcer
        required: true
        type: string
      - in: body
        name: enforcer
        description: The enforcer object
        required: true
        schema:
          $ref: '#/definitions/object'
      responses:
        "200":
          description: ""
          schema:
            $ref: '#/definitions/object.Enforcer'
  /api/update-group:
    post:
      tags:
      - Group API
      description: update group
      operationId: ApiController.UpdateGroup
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the group
        required: true
        type: string
      - in: body
        name: body
        description: The details of the group
        required: true
        schema:
          $ref: '#/definitions/object.Group'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-invitation:
    post:
      tags:
      - Invitation API
      description: update invitation
      operationId: ApiController.UpdateInvitation
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the invitation
        required: true
        type: string
      - in: body
        name: body
        description: The details of the invitation
        required: true
        schema:
          $ref: '#/definitions/object.Invitation'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-ldap:
    post:
      tags:
      - Account API
      description: update ldap
      operationId: ApiController.UpdateLdap
      parameters:
      - in: body
        name: body
        description: The details of the ldap
        required: true
        schema:
          $ref: '#/definitions/object.Ldap'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-model:
    post:
      tags:
      - Model API
      description: update model
      operationId: ApiController.UpdateModel
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the model
        required: true
        type: string
      - in: body
        name: body
        description: The details of the model
        required: true
        schema:
          $ref: '#/definitions/object.Model'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-organization:
    post:
      tags:
      - Organization API
      description: update organization
      operationId: ApiController.UpdateOrganization
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the organization
        required: true
        type: string
      - in: body
        name: body
        description: The details of the organization
        required: true
        schema:
          $ref: '#/definitions/object.Organization'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-payment:
    post:
      tags:
      - Payment API
      description: update payment
      operationId: ApiController.UpdatePayment
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the payment
        required: true
        type: string
      - in: body
        name: body
        description: The details of the payment
        required: true
        schema:
          $ref: '#/definitions/object.Payment'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-permission:
    post:
      tags:
      - Permission API
      description: update permission
      operationId: ApiController.UpdatePermission
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the permission
        required: true
        type: string
      - in: body
        name: body
        description: The details of the permission
        required: true
        schema:
          $ref: '#/definitions/object.Permission'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-plan:
    post:
      tags:
      - Plan API
      description: update plan
      operationId: ApiController.UpdatePlan
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the plan
        required: true
        type: string
      - in: body
        name: body
        description: The details of the plan
        required: true
        schema:
          $ref: '#/definitions/object.Plan'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-pricing:
    post:
      tags:
      - Pricing API
      description: update pricing
      operationId: ApiController.UpdatePricing
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the pricing
        required: true
        type: string
      - in: body
        name: body
        description: The details of the pricing
        required: true
        schema:
          $ref: '#/definitions/object.Pricing'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-product:
    post:
      tags:
      - Product API
      description: update product
      operationId: ApiController.UpdateProduct
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the product
        required: true
        type: string
      - in: body
        name: body
        description: The details of the product
        required: true
        schema:
          $ref: '#/definitions/object.Product'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-provider:
    post:
      tags:
      - Provider API
      description: update provider
      operationId: ApiController.UpdateProvider
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the provider
        required: true
        type: string
      - in: body
        name: body
        description: The details of the provider
        required: true
        schema:
          $ref: '#/definitions/object.Provider'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-resource:
    post:
      tags:
      - Resource API
      description: get resource
      operationId: ApiController.UpdateResource
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of resource
        required: true
        type: string
      - in: body
        name: resource
        description: The resource object
        required: true
        schema:
          $ref: '#/definitions/object.Resource'
      responses:
        "200":
          description: Success or error
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-role:
    post:
      tags:
      - Role API
      description: update role
      operationId: ApiController.UpdateRole
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the role
        required: true
        type: string
      - in: body
        name: body
        description: The details of the role
        required: true
        schema:
          $ref: '#/definitions/object.Role'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-session:
    post:
      tags:
      - Session API
      description: Update session for one user in one application.
      operationId: ApiController.UpdateSession
      parameters:
      - in: query
        name: id
        description: The id(organization/application/user) of session
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            type: array
            items:
              type: string
  /api/update-subscription:
    post:
      tags:
      - Subscription API
      description: update subscription
      operationId: ApiController.UpdateSubscription
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the subscription
        required: true
        type: string
      - in: body
        name: body
        description: The details of the subscription
        required: true
        schema:
          $ref: '#/definitions/object.Subscription'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-syncer:
    post:
      tags:
      - Syncer API
      description: update syncer
      operationId: ApiController.UpdateSyncer
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the syncer
        required: true
        type: string
      - in: body
        name: body
        description: The details of the syncer
        required: true
        schema:
          $ref: '#/definitions/object.Syncer'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-token:
    post:
      tags:
      - Token API
      description: update token
      operationId: ApiController.UpdateToken
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of token
        required: true
        type: string
      - in: body
        name: body
        description: Details of the token
        required: true
        schema:
          $ref: '#/definitions/object.Token'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-user:
    post:
      tags:
      - User API
      description: update user
      operationId: ApiController.UpdateUser
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the user
        required: true
        type: string
      - in: body
        name: body
        description: The details of the user
        required: true
        schema:
          $ref: '#/definitions/object.User'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/update-webhook:
    post:
      tags:
      - Webhook API
      description: update webhook
      operationId: ApiController.UpdateWebhook
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the webhook
        required: true
        type: string
        default: built-in/admin
      - in: body
        name: body
        description: The details of the webhook
        required: true
        schema:
          $ref: '#/definitions/object.Webhook'
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/upload-resource:
    post:
      tags:
      - Resource API
      operationId: ApiController.UploadResource
      parameters:
      - in: query
        name: owner
        description: Owner
        required: true
        type: string
      - in: query
        name: user
        description: User
        required: true
        type: string
      - in: query
        name: application
        description: Application
        required: true
        type: string
      - in: query
        name: tag
        description: Tag
        type: string
      - in: query
        name: parent
        description: Parent
        type: string
      - in: query
        name: fullFilePath
        description: Full File Path
        required: true
        type: string
      - in: query
        name: createdTime
        description: Created Time
        type: string
      - in: query
        name: description
        description: Description
        type: string
      - in: formData
        name: file
        description: Resource file
        required: true
        type: file
      responses:
        "200":
          description: FileUrl, objectKey
          schema:
            $ref: '#/definitions/object.Resource'
  /api/user:
    get:
      tags:
      - Account API
      description: return Laravel compatible user information according to OAuth 2.0
      operationId: ApiController.UserInfo2
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.LaravelResponse'
  /api/userinfo:
    get:
      tags:
      - Account API
      description: return user information according to OIDC standards
      operationId: ApiController.UserInfo
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Userinfo'
  /api/verify-captcha:
    post:
      tags:
      - Verification API
      operationId: ApiController.VerifyCaptcha
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Userinfo'
  /api/verify-code:
    post:
      tags:
      - Verification API
      operationId: ApiController.VerifyCode
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Userinfo'
  /api/verify-invitation:
    get:
      tags:
      - Invitation API
      description: verify invitation
      operationId: ApiController.VerifyInvitation
      parameters:
      - in: query
        name: id
        description: The id ( owner/name ) of the invitation
        required: true
        type: string
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/webauthn/signin/begin:
    get:
      tags:
      - Login API
      description: WebAuthn Login Flow 1st stage
      operationId: ApiController.WebAuthnSigninBegin
      parameters:
      - in: query
        name: owner
        description: owner
        required: true
        type: string
      - in: query
        name: name
        description: name
        required: true
        type: string
      responses:
        "200":
          description: The CredentialAssertion object
          schema:
            $ref: '#/definitions/protocol.CredentialAssertion'
  /api/webauthn/signin/finish:
    post:
      tags:
      - Login API
      description: WebAuthn Login Flow 2nd stage
      operationId: ApiController.WebAuthnSigninFinish
      parameters:
      - in: body
        name: body
        description: authenticator assertion Response
        required: true
        schema:
          $ref: '#/definitions/protocol.CredentialAssertionResponse'
      responses:
        "200":
          description: '"The Response object"'
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/webauthn/signup/begin:
    get:
      tags:
      - User API
      description: WebAuthn Registration Flow 1st stage
      operationId: ApiController.WebAuthnSignupBegin
      responses:
        "200":
          description: The CredentialCreationOptions object
          schema:
            $ref: '#/definitions/protocol.CredentialCreation'
  /api/webauthn/signup/finish:
    post:
      tags:
      - User API
      description: WebAuthn Registration Flow 2nd stage
      operationId: ApiController.WebAuthnSignupFinish
      parameters:
      - in: body
        name: body
        description: authenticator attestation Response
        required: true
        schema:
          $ref: '#/definitions/protocol.CredentialCreationResponse'
      responses:
        "200":
          description: '"The Response object"'
          schema:
            $ref: '#/definitions/controllers.Response'
  /api/webhook:
    post:
      tags:
      - System API
      operationId: ApiController.HandleOfficialAccountEvent
      responses:
        "200":
          description: The Response object
          schema:
            $ref: '#/definitions/object.Userinfo'
definitions:
  casbin.Enforcer:
    title: Enforcer
    type: object
  controllers.AuthForm:
    title: AuthForm
    type: object
  controllers.EmailForm:
    title: EmailForm
    type: object
    properties:
      content:
        type: string
      provider:
        type: string
      receivers:
        type: array
        items:
          type: string
      sender:
        type: string
      title:
        type: string
  controllers.LaravelResponse:
    title: LaravelResponse
    type: object
    properties:
      created_at:
        type: string
      email:
        type: string
      email_verified_at:
        type: string
      id:
        type: string
      name:
        type: string
      updated_at:
        type: string
  controllers.LdapResp:
    title: LdapResp
    type: object
    properties:
      existUuids:
        type: array
        items:
          type: string
      users:
        type: array
        items:
          $ref: '#/definitions/object.LdapUser'
  controllers.LdapSyncResp:
    title: LdapSyncResp
    type: object
    properties:
      exist:
        type: array
        items:
          $ref: '#/definitions/object.LdapUser'
      failed:
        type: array
        items:
          $ref: '#/definitions/object.LdapUser'
  controllers.NotificationForm:
    title: NotificationForm
    type: object
    properties:
      content:
        type: string
  controllers.Response:
    title: Response
    type: object
    properties:
      data:
        additionalProperties:
          description: support string, struct or []struct
          type: string
      data2:
        additionalProperties:
          description: support string, struct or []struct
          type: string
      msg:
        type: string
      name:
        type: string
      status:
        type: string
      sub:
        type: string
  controllers.SmsForm:
    title: SmsForm
    type: object
    properties:
      content:
        type: string
      organizationId:
        type: string
      receivers:
        type: array
        items:
          type: string
  jose.JSONWebKey:
    title: JSONWebKey
    type: object
  model.Model:
    title: Model
    type: object
  object:
    title: object
    type: object
  object.AccountItem:
    title: AccountItem
    type: object
    properties:
      modifyRule:
        type: string
      name:
        type: string
      viewRule:
        type: string
      visible:
        type: boolean
  object.Adapter:
    title: Adapter
    type: object
    properties:
      createdTime:
        type: string
      database:
        type: string
      databaseType:
        type: string
      host:
        type: string
      name:
        type: string
      owner:
        type: string
      password:
        type: string
      port:
        type: integer
        format: int64
      table:
        type: string
      type:
        type: string
      useSameDb:
        type: boolean
      user:
        type: string
  object.Application:
    title: Application
    type: object
    properties:
      affiliationUrl:
        type: string
      cert:
        type: string
      certPublicKey:
        type: string
      clientId:
        type: string
      clientSecret:
        type: string
      createdTime:
        type: string
      description:
        type: string
      displayName:
        type: string
      enableAutoSignin:
        type: boolean
      enableCodeSignin:
        type: boolean
      enableLinkWithEmail:
        type: boolean
      enablePassword:
        type: boolean
      enableSamlC14n10:
        type: boolean
      enableSamlCompress:
        type: boolean
      enableSignUp:
        type: boolean
      enableSigninSession:
        type: boolean
      enableWebAuthn:
        type: boolean
      expireInHours:
        type: integer
        format: int64
      failedSigninFrozenTime:
        type: integer
        format: int64
      failedSigninLimit:
        type: integer
        format: int64
      forgetUrl:
        type: string
      formBackgroundUrl:
        type: string
      formCss:
        type: string
      formCssMobile:
        type: string
      formOffset:
        type: integer
        format: int64
      formSideHtml:
        type: string
      grantTypes:
        type: array
        items:
          type: string
      homepageUrl:
        type: string
      invitationCodes:
        type: array
        items:
          type: string
      logo:
        type: string
      name:
        type: string
      orgChoiceMode:
        type: string
      organization:
        type: string
      organizationObj:
        $ref: '#/definitions/object.Organization'
      owner:
        type: string
      providers:
        type: array
        items:
          $ref: '#/definitions/object.ProviderItem'
      redirectUris:
        type: array
        items:
          type: string
      refreshExpireInHours:
        type: integer
        format: int64
      samlAttributes:
        type: array
        items:
          $ref: '#/definitions/object.SamlItem'
      samlReplyUrl:
        type: string
      signinHtml:
        type: string
      signinMethods:
        type: array
        items:
          $ref: '#/definitions/object.SigninMethod'
      signinUrl:
        type: string
      signupHtml:
        type: string
      signupItems:
        type: array
        items:
          $ref: '#/definitions/object.SignupItem'
      signupUrl:
        type: string
      tags:
        type: array
        items:
          type: string
      termsOfUse:
        type: string
      themeData:
        $ref: '#/definitions/object.ThemeData'
      tokenFields:
        type: array
        items:
          type: string
      tokenFormat:
        type: string
  object.Cert:
    title: Cert
    type: object
    properties:
      bitSize:
        type: integer
        format: int64
      certificate:
        type: string
      createdTime:
        type: string
      cryptoAlgorithm:
        type: string
      displayName:
        type: string
      expireInYears:
        type: integer
        format: int64
      name:
        type: string
      owner:
        type: string
      privateKey:
        type: string
      scope:
        type: string
      type:
        type: string
  object.Enforcer:
    title: Enforcer
    type: object
    properties:
      adapter:
        type: string
      createdTime:
        type: string
      description:
        type: string
      displayName:
        type: string
      model:
        type: string
      modelCfg:
        additionalProperties:
          type: string
      name:
        type: string
      owner:
        type: string
      updatedTime:
        type: string
  object.GaugeVecInfo:
    title: GaugeVecInfo
    type: object
    properties:
      method:
        type: string
      name:
        type: string
      throughput:
        type: number
        format: double
  object.Group:
    title: Group
    type: object
    properties:
      children:
        type: array
        items:
          $ref: '#/definitions/object.Group'
      contactEmail:
        type: string
      createdTime:
        type: string
      displayName:
        type: string
      isEnabled:
        type: boolean
      isTopGroup:
        type: boolean
      key:
        type: string
      manager:
        type: string
      name:
        type: string
      owner:
        type: string
      parentId:
        type: string
      title:
        type: string
      type:
        type: string
      updatedTime:
        type: string
      users:
        type: array
        items:
          $ref: '#/definitions/object.User'
  object.Header:
    title: Header
    type: object
    properties:
      name:
        type: string
      value:
        type: string
  object.HistogramVecInfo:
    title: HistogramVecInfo
    type: object
    properties:
      count:
        type: integer
        format: int64
      latency:
        type: string
      method:
        type: string
      name:
        type: string
  object.IntrospectionResponse:
    title: IntrospectionResponse
    type: object
    properties:
      active:
        type: boolean
      aud:
        type: array
        items:
          type: string
      client_id:
        type: string
      exp:
        type: integer
        format: int64
      iat:
        type: integer
        format: int64
      iss:
        type: string
      jti:
        type: string
      nbf:
        type: integer
        format: int64
      scope:
        type: string
      sub:
        type: string
      token_type:
        type: string
      username:
        type: string
  object.Invitation:
    title: Invitation
    type: object
    properties:
      application:
        type: string
      code:
        type: string
      createdTime:
        type: string
      displayName:
        type: string
      email:
        type: string
      name:
        type: string
      owner:
        type: string
      phone:
        type: string
      quota:
        type: integer
        format: int64
      signupGroup:
        type: string
      state:
        type: string
      updatedTime:
        type: string
      usedCount:
        type: integer
        format: int64
      username:
        type: string
  object.Ldap:
    title: Ldap
    type: object
    properties:
      autoSync:
        type: integer
        format: int64
      baseDn:
        type: string
      createdTime:
        type: string
      enableSsl:
        type: boolean
      filter:
        type: string
      filterFields:
        type: array
        items:
          type: string
      host:
        type: string
      id:
        type: string
      lastSync:
        type: string
      owner:
        type: string
      password:
        type: string
      port:
        type: integer
        format: int64
      serverName:
        type: string
      username:
        type: string
  object.LdapUser:
    title: LdapUser
    type: object
    properties:
      EmailAddress:
        type: string
      Mail:
        type: string
      MobileTelephoneNumber:
        type: string
      PostalAddress:
        type: string
      RegisteredAddress:
        type: string
      TelephoneNumber:
        type: string
      address:
        type: string
      cn:
        type: string
      displayName:
        type: string
      email:
        type: string
      gidNumber:
        type: string
      groupId:
        type: string
      memberOf:
        type: string
      mobile:
        type: string
      uid:
        type: string
      uidNumber:
        type: string
      userPrincipalName:
        type: string
      uuid:
        type: string
  object.ManagedAccount:
    title: ManagedAccount
    type: object
    properties:
      application:
        type: string
      password:
        type: string
      signinUrl:
        type: string
      username:
        type: string
  object.MfaItem:
    title: MfaItem
    type: object
    properties:
      name:
        type: string
      rule:
        type: string
  object.MfaProps:
    title: MfaProps
    type: object
    properties:
      countryCode:
        type: string
      enabled:
        type: boolean
      isPreferred:
        type: boolean
      mfaType:
        type: string
      recoveryCodes:
        type: array
        items:
          type: string
      secret:
        type: string
      url:
        type: string
  object.Model:
    title: Model
    type: object
    properties:
      createdTime:
        type: string
      description:
        type: string
      displayName:
        type: string
      modelText:
        type: string
      name:
        type: string
      owner:
        type: string
  object.OidcDiscovery:
    title: OidcDiscovery
    type: object
    properties:
      authorization_endpoint:
        type: string
      claims_supported:
        type: array
        items:
          type: string
      end_session_endpoint:
        type: string
      grant_types_supported:
        type: array
        items:
          type: string
      id_token_signing_alg_values_supported:
        type: array
        items:
          type: string
      introspection_endpoint:
        type: string
      issuer:
        type: string
      jwks_uri:
        type: string
      request_object_signing_alg_values_supported:
        type: array
        items:
          type: string
      request_parameter_supported:
        type: boolean
      response_modes_supported:
        type: array
        items:
          type: string
      response_types_supported:
        type: array
        items:
          type: string
      scopes_supported:
        type: array
        items:
          type: string
      subject_types_supported:
        type: array
        items:
          type: string
      token_endpoint:
        type: string
      userinfo_endpoint:
        type: string
  object.Organization:
    title: Organization
    type: object
    properties:
      accountItems:
        type: array
        items:
          $ref: '#/definitions/object.AccountItem'
      countryCodes:
        type: array
        items:
          type: string
      createdTime:
        type: string
      defaultApplication:
        type: string
      defaultAvatar:
        type: string
      defaultPassword:
        type: string
      displayName:
        type: string
      enableSoftDeletion:
        type: boolean
      favicon:
        type: string
      initScore:
        type: integer
        format: int64
      isProfilePublic:
        type: boolean
      languages:
        type: array
        items:
          type: string
      masterPassword:
        type: string
      masterVerificationCode:
        type: string
      mfaItems:
        type: array
        items:
          $ref: '#/definitions/object.MfaItem'
      name:
        type: string
      owner:
        type: string
      passwordOptions:
        type: array
        items:
          type: string
      passwordSalt:
        type: string
      passwordType:
        type: string
      tags:
        type: array
        items:
          type: string
      themeData:
        $ref: '#/definitions/object.ThemeData'
      websiteUrl:
        type: string
  object.Ormer:
    title: Ormer
    type: object
    properties:
      Engine:
        $ref: '#/definitions/xorm.Engine'
      dataSourceName:
        type: string
      dbName:
        type: string
      driverName:
        type: string
  object.Payment:
    title: Payment
    type: object
    properties:
      createdTime:
        type: string
      currency:
        type: string
      detail:
        type: string
      displayName:
        type: string
      invoiceRemark:
        type: string
      invoiceTaxId:
        type: string
      invoiceTitle:
        type: string
      invoiceType:
        type: string
      invoiceUrl:
        type: string
      message:
        type: string
      name:
        type: string
      outOrderId:
        type: string
      owner:
        type: string
      payUrl:
        type: string
      personEmail:
        type: string
      personIdCard:
        type: string
      personName:
        type: string
      personPhone:
        type: string
      price:
        type: number
        format: double
      productDisplayName:
        type: string
      productName:
        type: string
      provider:
        type: string
      returnUrl:
        type: string
      state:
        $ref: '#/definitions/pp.PaymentState'
      successUrl:
        type: string
      tag:
        type: string
      type:
        type: string
      user:
        type: string
  object.Permission:
    title: Permission
    type: object
    properties:
      actions:
        type: array
        items:
          type: string
      adapter:
        type: string
      approveTime:
        type: string
      approver:
        type: string
      createdTime:
        type: string
      description:
        type: string
      displayName:
        type: string
      domains:
        type: array
        items:
          type: string
      effect:
        type: string
      groups:
        type: array
        items:
          type: string
      isEnabled:
        type: boolean
      model:
        type: string
      name:
        type: string
      owner:
        type: string
      resourceType:
        type: string
      resources:
        type: array
        items:
          type: string
      roles:
        type: array
        items:
          type: string
      state:
        type: string
      submitter:
        type: string
      users:
        type: array
        items:
          type: string
  object.Plan:
    title: Plan
    type: object
    properties:
      createdTime:
        type: string
      currency:
        type: string
      description:
        type: string
      displayName:
        type: string
      isEnabled:
        type: boolean
      name:
        type: string
      options:
        type: array
        items:
          type: string
      owner:
        type: string
      paymentProviders:
        type: array
        items:
          type: string
      period:
        type: string
      price:
        type: number
        format: double
      product:
        type: string
      role:
        type: string
  object.Pricing:
    title: Pricing
    type: object
    properties:
      application:
        type: string
      createdTime:
        type: string
      description:
        type: string
      displayName:
        type: string
      isEnabled:
        type: boolean
      name:
        type: string
      owner:
        type: string
      plans:
        type: array
        items:
          type: string
      trialDuration:
        type: integer
        format: int64
  object.Product:
    title: Product
    type: object
    properties:
      createdTime:
        type: string
      currency:
        type: string
      description:
        type: string
      detail:
        type: string
      displayName:
        type: string
      image:
        type: string
      name:
        type: string
      owner:
        type: string
      price:
        type: number
        format: double
      providerObjs:
        type: array
        items:
          $ref: '#/definitions/object.Provider'
      providers:
        type: array
        items:
          type: string
      quantity:
        type: integer
        format: int64
      returnUrl:
        type: string
      sold:
        type: integer
        format: int64
      state:
        type: string
      tag:
        type: string
  object.PrometheusInfo:
    title: PrometheusInfo
    type: object
    properties:
      apiLatency:
        type: array
        items:
          $ref: '#/definitions/object.HistogramVecInfo'
      apiThroughput:
        type: array
        items:
          $ref: '#/definitions/object.GaugeVecInfo'
      totalThroughput:
        type: number
        format: double
  object.Provider:
    title: Provider
    type: object
    properties:
      appId:
        type: string
      bucket:
        type: string
      category:
        type: string
      cert:
        type: string
      clientId:
        type: string
      clientId2:
        type: string
      clientSecret:
        type: string
      clientSecret2:
        type: string
      content:
        type: string
      createdTime:
        type: string
      customAuthUrl:
        type: string
      customLogo:
        type: string
      customTokenUrl:
        type: string
      customUserInfoUrl:
        type: string
      disableSsl:
        type: boolean
      displayName:
        type: string
      domain:
        type: string
      enableSignAuthnRequest:
        type: boolean
      endpoint:
        type: string
      host:
        type: string
      idP:
        type: string
      intranetEndpoint:
        type: string
      issuerUrl:
        type: string
      metadata:
        type: string
      method:
        type: string
      name:
        type: string
      owner:
        type: string
      pathPrefix:
        type: string
      port:
        type: integer
        format: int64
      providerUrl:
        type: string
      receiver:
        type: string
      regionId:
        type: string
      scopes:
        type: string
      signName:
        type: string
      subType:
        type: string
      templateCode:
        type: string
      title:
        type: string
      type:
        type: string
      userMapping:
        additionalProperties:
          type: string
  object.ProviderItem:
    title: ProviderItem
    type: object
    properties:
      canSignIn:
        type: boolean
      canSignUp:
        type: boolean
      canUnlink:
        type: boolean
      name:
        type: string
      owner:
        type: string
      prompted:
        type: boolean
      provider:
        $ref: '#/definitions/object.Provider'
      rule:
        type: string
      signupGroup:
        type: string
  object.Resource:
    title: Resource
    type: object
    properties:
      application:
        type: string
      createdTime:
        type: string
      description:
        type: string
      fileFormat:
        type: string
      fileName:
        type: string
      fileSize:
        type: integer
        format: int64
      fileType:
        type: string
      name:
        type: string
      owner:
        type: string
      parent:
        type: string
      provider:
        type: string
      tag:
        type: string
      url:
        type: string
      user:
        type: string
  object.Role:
    title: Role
    type: object
    properties:
      createdTime:
        type: string
      description:
        type: string
      displayName:
        type: string
      domains:
        type: array
        items:
          type: string
      groups:
        type: array
        items:
          type: string
      isEnabled:
        type: boolean
      name:
        type: string
      owner:
        type: string
      roles:
        type: array
        items:
          type: string
      users:
        type: array
        items:
          type: string
  object.SamlItem:
    title: SamlItem
    type: object
    properties:
      name:
        type: string
      nameFormat:
        type: string
      value:
        type: string
  object.SigninMethod:
    title: SigninMethod
    type: object
    properties:
      displayName:
        type: string
      name:
        type: string
      rule:
        type: string
  object.SignupItem:
    title: SignupItem
    type: object
    properties:
      label:
        type: string
      name:
        type: string
      placeholder:
        type: string
      prompted:
        type: boolean
      regex:
        type: string
      required:
        type: boolean
      rule:
        type: string
      visible:
        type: boolean
  object.Subscription:
    title: Subscription
    type: object
    properties:
      createdTime:
        type: string
      description:
        type: string
      displayName:
        type: string
      endTime:
        type: string
        format: datetime
      name:
        type: string
      owner:
        type: string
      payment:
        type: string
      period:
        type: string
      plan:
        type: string
      pricing:
        type: string
      startTime:
        type: string
        format: datetime
      state:
        $ref: '#/definitions/object.SubscriptionState'
      user:
        type: string
  object.SubscriptionState:
    title: SubscriptionState
    type: string
    enum:
    - SubStatePending = "Pending"
    - SubStateError = "Error"
    - SubStateSuspended = "Suspended"
    - SubStateActive = "Active"
    - SubStateUpcoming = "Upcoming"
    - SubStateExpired = "Expired"
    example: Pending
  object.Syncer:
    title: Syncer
    type: object
    properties:
      affiliationTable:
        type: string
      avatarBaseUrl:
        type: string
      createdTime:
        type: string
      database:
        type: string
      databaseType:
        type: string
      errorText:
        type: string
      host:
        type: string
      isEnabled:
        type: boolean
      isReadOnly:
        type: boolean
      name:
        type: string
      organization:
        type: string
      owner:
        type: string
      password:
        type: string
      port:
        type: integer
        format: int64
      sslMode:
        type: string
      syncInterval:
        type: integer
        format: int64
      table:
        type: string
      tableColumns:
        type: array
        items:
          $ref: '#/definitions/object.TableColumn'
      type:
        type: string
      user:
        type: string
  object.TableColumn:
    title: TableColumn
    type: object
    properties:
      casdoorName:
        type: string
      isHashed:
        type: boolean
      isKey:
        type: boolean
      name:
        type: string
      type:
        type: string
      values:
        type: array
        items:
          type: string
  object.ThemeData:
    title: ThemeData
    type: object
    properties:
      borderRadius:
        type: integer
        format: int64
      colorPrimary:
        type: string
      isCompact:
        type: boolean
      isEnabled:
        type: boolean
      themeType:
        type: string
  object.Token:
    title: Token
    type: object
    properties:
      accessToken:
        type: string
      accessTokenHash:
        type: string
      application:
        type: string
      code:
        type: string
      codeChallenge:
        type: string
      codeExpireIn:
        type: integer
        format: int64
      codeIsUsed:
        type: boolean
      createdTime:
        type: string
      expiresIn:
        type: integer
        format: int64
      name:
        type: string
      organization:
        type: string
      owner:
        type: string
      refreshToken:
        type: string
      refreshTokenHash:
        type: string
      scope:
        type: string
      tokenType:
        type: string
      user:
        type: string
  object.TokenError:
    title: TokenError
    type: object
    properties:
      error:
        type: string
      error_description:
        type: string
  object.TokenWrapper:
    title: TokenWrapper
    type: object
    properties:
      access_token:
        type: string
      expires_in:
        type: integer
        format: int64
      id_token:
        type: string
      refresh_token:
        type: string
      scope:
        type: string
      token_type:
        type: string
  object.User:
    title: User
    type: object
    properties:
      accessKey:
        type: string
      accessSecret:
        type: string
      address:
        type: array
        items:
          type: string
      adfs:
        type: string
      affiliation:
        type: string
      alipay:
        type: string
      amazon:
        type: string
      apple:
        type: string
      auth0:
        type: string
      avatar:
        type: string
      avatarType:
        type: string
      azuread:
        type: string
      azureadb2c:
        type: string
      baidu:
        type: string
      battlenet:
        type: string
      bilibili:
        type: string
      bio:
        type: string
      birthday:
        type: string
      bitbucket:
        type: string
      box:
        type: string
      casdoor:
        type: string
      cloudfoundry:
        type: string
      countryCode:
        type: string
      createdIp:
        type: string
      createdTime:
        type: string
      custom:
        type: string
      dailymotion:
        type: string
      deezer:
        type: string
      deletedTime:
        type: string
      digitalocean:
        type: string
      dingtalk:
        type: string
      discord:
        type: string
      displayName:
        type: string
      douyin:
        type: string
      dropbox:
        type: string
      education:
        type: string
      email:
        type: string
      emailVerified:
        type: boolean
      eveonline:
        type: string
      externalId:
        type: string
      facebook:
        type: string
      firstName:
        type: string
      fitbit:
        type: string
      gender:
        type: string
      gitea:
        type: string
      gitee:
        type: string
      github:
        type: string
      gitlab:
        type: string
      google:
        type: string
      groups:
        type: array
        items:
          type: string
      hash:
        type: string
      heroku:
        type: string
      homepage:
        type: string
      id:
        type: string
      idCard:
        type: string
      idCardType:
        type: string
      influxcloud:
        type: string
      infoflow:
        type: string
      instagram:
        type: string
      intercom:
        type: string
      isAdmin:
        type: boolean
      isDefaultAvatar:
        type: boolean
      isDeleted:
        type: boolean
      isForbidden:
        type: boolean
      isOnline:
        type: boolean
      kakao:
        type: string
      karma:
        type: integer
        format: int64
      kwai:
        type: string
      language:
        type: string
      lark:
        type: string
      lastName:
        type: string
      lastSigninIp:
        type: string
      lastSigninTime:
        type: string
      lastSigninWrongTime:
        type: string
      lastfm:
        type: string
      ldap:
        type: string
      line:
        type: string
      linkedin:
        type: string
      location:
        type: string
      mailru:
        type: string
      managedAccounts:
        type: array
        items:
          $ref: '#/definitions/object.ManagedAccount'
      meetup:
        type: string
      metamask:
        type: string
      mfaEmailEnabled:
        type: boolean
      mfaPhoneEnabled:
        type: boolean
      microsoftonline:
        type: string
      multiFactorAuths:
        type: array
        items:
          $ref: '#/definitions/object.MfaProps'
      name:
        type: string
      naver:
        type: string
      nextcloud:
        type: string
      okta:
        type: string
      onedrive:
        type: string
      oura:
        type: string
      owner:
        type: string
      password:
        type: string
      passwordSalt:
        type: string
      passwordType:
        type: string
      patreon:
        type: string
      paypal:
        type: string
      permanentAvatar:
        type: string
      permissions:
        type: array
        items:
          $ref: '#/definitions/object.Permission'
      phone:
        type: string
      preHash:
        type: string
      preferredMfaType:
        type: string
      properties:
        additionalProperties:
          type: string
      qq:
        type: string
      ranking:
        type: integer
        format: int64
      recoveryCodes:
        type: array
        items:
          type: string
      region:
        type: string
      roles:
        type: array
        items:
          $ref: '#/definitions/object.Role'
      salesforce:
        type: string
      score:
        type: integer
        format: int64
      shopify:
        type: string
      signinWrongTimes:
        type: integer
        format: int64
      signupApplication:
        type: string
      slack:
        type: string
      soundcloud:
        type: string
      spotify:
        type: string
      steam:
        type: string
      strava:
        type: string
      stripe:
        type: string
      tag:
        type: string
      tiktok:
        type: string
      title:
        type: string
      totpSecret:
        type: string
      tumblr:
        type: string
      twitch:
        type: string
      twitter:
        type: string
      type:
        type: string
      typetalk:
        type: string
      uber:
        type: string
      updatedTime:
        type: string
      vk:
        type: string
      web3onboard:
        type: string
      webauthnCredentials:
        type: array
        items:
          $ref: '#/definitions/webauthn.Credential'
      wechat:
        type: string
      wecom:
        type: string
      weibo:
        type: string
      wepay:
        type: string
      xero:
        type: string
      yahoo:
        type: string
      yammer:
        type: string
      yandex:
        type: string
      zoom:
        type: string
  object.Userinfo:
    title: Userinfo
    type: object
    properties:
      address:
        type: string
      aud:
        type: string
      email:
        type: string
      email_verified:
        type: boolean
      groups:
        type: array
        items:
          type: string
      iss:
        type: string
      name:
        type: string
      phone:
        type: string
      picture:
        type: string
      preferred_username:
        type: string
      sub:
        type: string
  object.Webhook:
    title: Webhook
    type: object
    properties:
      contentType:
        type: string
      createdTime:
        type: string
      events:
        type: array
        items:
          type: string
      headers:
        type: array
        items:
          $ref: '#/definitions/object.Header'
      isEnabled:
        type: boolean
      isUserExtended:
        type: boolean
      method:
        type: string
      name:
        type: string
      organization:
        type: string
      owner:
        type: string
      url:
        type: string
  pp.PaymentState:
    title: PaymentState
    type: string
    enum:
    - PaymentStatePaid = "Paid"
    - PaymentStateCreated = "Created"
    - PaymentStateCanceled = "Canceled"
    - PaymentStateTimeout = "Timeout"
    - PaymentStateError = "Error"
    example: Paid
  protocol.CredentialAssertion:
    title: CredentialAssertion
    type: object
  protocol.CredentialAssertionResponse:
    title: CredentialAssertionResponse
    type: object
  protocol.CredentialCreation:
    title: CredentialCreation
    type: object
  protocol.CredentialCreationResponse:
    title: CredentialCreationResponse
    type: object
  util.SystemInfo:
    title: SystemInfo
    type: object
    properties:
      cpuUsage:
        type: array
        items:
          type: number
          format: double
      memoryTotal:
        type: integer
        format: int64
      memoryUsed:
        type: integer
        format: int64
  util.VersionInfo:
    title: VersionInfo
    type: object
    properties:
      commitId:
        type: string
      commitOffset:
        type: integer
        format: int64
      version:
        type: string
  webauthn.Credential:
    title: Credential
    type: object
  xorm.Engine:
    title: Engine
    type: object
  xormadapter.Adapter:
    title: Adapter
    type: object
securityDefinitions:
  AccessToken:
    type: apiKey
    name: Authorization
    in: header
