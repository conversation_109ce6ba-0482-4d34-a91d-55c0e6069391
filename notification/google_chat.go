// Copyright 2023 The Casdoor Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package notification

import (
	"context"
	"strings"

	"github.com/casdoor/notify"
	"github.com/casdoor/notify/service/googlechat"
	"google.golang.org/api/chat/v1"
	"google.golang.org/api/option"
)

func NewGoogleChatProvider(credentials string) (*notify.Notify, error) {
	withCred := option.WithCredentialsJSON([]byte(credentials))
	withSpacesScope := option.WithScopes("https://www.googleapis.com/auth/chat.spaces")

	listSvc, err := chat.NewService(context.Background(), withCred, withSpacesScope)
	spaces, err := listSvc.Spaces.List().Do()
	if err != nil {
		return nil, err
	}

	receivers := make([]string, 0)
	for _, space := range spaces.Spaces {
		name := strings.Replace(space.Name, "spaces/", "", 1)
		receivers = append(receivers, name)
	}

	googleChatSrv, err := googlechat.New(withCred)
	if err != nil {
		return nil, err
	}

	googleChatSrv.AddReceivers(receivers...)

	notifier := notify.NewWithServices(googleChatSrv)

	return notifier, nil
}
