{"account": {"Failed to add user": "Gagal tambah pengguna", "Get init score failed, error: %w": "Gagal dapatkan skor awal, ralat: %w", "Please sign out first": "Sila log keluar dahulu", "The application does not allow to sign up new account": "Aplikasi tidak ben<PERSON>an pendaftaran akaun baharu"}, "auth": {"Challenge method should be S256": "Ka<PERSON>h cabaran mesti S256", "DeviceCode Invalid": "Kod Peranti Tidak Sah", "Failed to create user, user information is invalid: %s": "Gagal cipta pengguna, maklumat tidak sah: %s", "Failed to login in: %s": "Gagal log masuk: %s", "Invalid token": "Token tidak sah", "State expected: %s, but got: %s": "<PERSON><PERSON><PERSON> keadaan: %s, tetapi dapat: %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "<PERSON><PERSON>un untuk pembekal: %s dan nama pengguna: %s (%s) tidak wujud dan tidak dibenarkan daftar melalui %%s, sila guna cara lain", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "<PERSON>kaun untuk pembekal: %s dan nama pengguna: %s (%s) tidak wujud dan tidak dibenarkan daftar, sila hubungi sokongan IT", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "<PERSON><PERSON><PERSON> untuk pembekal: %s dan nama pengguna: %s (%s) sudah dipautkan kepada akaun lain: %s (%s)", "The application: %s does not exist": "Aplikasi: %s tidak wujud", "The login method: login with LDAP is not enabled for the application": "Kaedah log masuk LDAP tidak dibenarkan untuk aplikasi ini", "The login method: login with SMS is not enabled for the application": "Kaedah log masuk SMS tidak dibenarkan untuk aplikasi ini", "The login method: login with email is not enabled for the application": "Kaedah log masuk emel tidak dibenarkan untuk aplikasi ini", "The login method: login with face is not enabled for the application": "Kaedah log masuk muka tidak dibenarkan untuk aplikasi ini", "The login method: login with password is not enabled for the application": "Kaedah log masuk kata laluan tidak dibenarkan untuk aplikasi ini", "The organization: %s does not exist": "Organisasi: %s tidak wujud", "The provider: %s does not exist": "Pembekal: %s tidak wujud", "The provider: %s is not enabled for the application": "Pembekal: %s tidak dibenarkan untuk aplikasi ini", "Unauthorized operation": "Operasi tidak dibenarkan", "Unknown authentication type (not password or provider), form = %s": "<PERSON><PERSON> tida<PERSON> (bukan kata laluan atau pembekal), borang = %s", "User's tag: %s is not listed in the application's tags": "Tag pengguna: %s tidak tersenarai dalam tag aplikasi", "UserCode Expired": "Kod Pen<PERSON>una Tamat", "UserCode Invalid": "Kod Pengguna Tidak Sah", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "Pengguna berbayar %s tiada langganan aktif atau tertunda dan aplikasi: %s tiada harga lalai", "the application for user %s is not found": "Aplikasi untuk pengguna %s tidak ditemui", "the organization: %s is not found": "Organisasi: %s tidak ditemui"}, "cas": {"Service %s and %s do not match": "Perkhidmatan %s dan %s tidak sepadan"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s tidak memenuhi format CIDR: %s", "Affiliation cannot be blank": "Afiliasi tidak boleh kosong", "CIDR for IP: %s should not be empty": "CIDR untuk IP: %s tidak boleh kosong", "Default code does not match the code's matching rules": "Kod lalai tidak sepadan dengan peraturan padanan", "DisplayName cannot be blank": "<PERSON>a paparan tidak boleh kosong", "DisplayName is not valid real name": "<PERSON>a paparan bukan nama sebenar yang sah", "Email already exists": "<PERSON><PERSON> sudah wujud", "Email cannot be empty": "<PERSON><PERSON> tidak boleh kosong", "Email is invalid": "<PERSON><PERSON> tidak sah", "Empty username.": "<PERSON>a pengguna kosong.", "Face data does not exist, cannot log in": "Data muka tiada, tidak boleh log masuk", "Face data mismatch": "Data muka tidak sepadan", "Failed to parse client IP: %s": "Gagal huraikan IP klien: %s", "FirstName cannot be blank": "<PERSON>a pertama tidak boleh kosong", "Invitation code cannot be blank": "Kod jemputan tidak boleh kosong", "Invitation code exhausted": "<PERSON><PERSON> jem<PERSON>an habis", "Invitation code is invalid": "Kod jemputan tidak sah", "Invitation code suspended": "<PERSON><PERSON> jemputan digantung", "LDAP user name or password incorrect": "<PERSON>a pengguna atau kata laluan LDAP salah", "LastName cannot be blank": "<PERSON>a te<PERSON>hir tidak boleh kosong", "Multiple accounts with same uid, please check your ldap server": "<PERSON><PERSON><PERSON> akaun dengan uid sama, sila semak pelayan ldap anda", "Organization does not exist": "Organisasi tidak wujud", "Password cannot be empty": "<PERSON>a laluan tidak boleh kosong", "Phone already exists": "Telefon sudah wujud", "Phone cannot be empty": "Telefon tidak boleh kosong", "Phone number is invalid": "Nombor telefon tidak sah", "Please register using the email corresponding to the invitation code": "<PERSON><PERSON> daftar dengan emel yang sepadan dengan kod jemputan", "Please register using the phone  corresponding to the invitation code": "<PERSON>la daftar dengan telefon yang sepadan dengan kod jemputan", "Please register using the username corresponding to the invitation code": "<PERSON>la daftar dengan nama pengguna yang sepadan dengan kod jemputan", "Session outdated, please login again": "<PERSON><PERSON> tamat, sila log masuk semula", "The invitation code has already been used": "<PERSON>d jemputan sudah digunakan", "The user is forbidden to sign in, please contact the administrator": "Pengguna dilarang log masuk, sila hubungi pentadbir", "The user: %s doesn't exist in LDAP server": "Pengguna: %s tidak wujud dalam pelayan LDAP", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "<PERSON>a pengguna hanya boleh mengan<PERSON><PERSON><PERSON>, garis bawah atau sengkang, tidak boleh ada sengkang atau garis bawah berturutan, dan tidak boleh bermula atau berakhir dengan sengkang atau garis bawah.", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "<PERSON><PERSON> \\\"%s\\\" untuk medan akaun \\\"%s\\\" tidak sepadan dengan regex", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "<PERSON><PERSON> \\\"%s\\\" untuk medan pendaftaran \\\"%s\\\" tidak sepadan dengan regex aplikasi \\\"%s\\\"", "Username already exists": "<PERSON>a pengguna sudah wujud", "Username cannot be an email address": "<PERSON><PERSON> pengguna tidak boleh jadi alamat emel", "Username cannot contain white spaces": "<PERSON>a pengguna tidak boleh ada ruang putih", "Username cannot start with a digit": "<PERSON>a pengguna tidak boleh bermula dengan nombor", "Username is too long (maximum is 255 characters).": "<PERSON><PERSON> pengguna terlalu panjang (maksimum 255 aksara).", "Username must have at least 2 characters": "Nama pengguna mesti sekurang-kurangnya 2 aksara", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "<PERSON>a pengguna menyokong format emel. <PERSON><PERSON>, nama hanya boleh <PERSON>, garis bawah atau sengkang, tanpa bert<PERSON>, tidak bermula atau berakhir dengan sengkang atau garis bawah.", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "<PERSON>a masukkan kata laluan atau kod salah terlalu banyak kali, sila tunggu %d minit dan cuba lagi", "Your IP address: %s has been banned according to the configuration of: ": "Alamat IP anda: %s telah disekat mengikut konfigurasi: ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "Kata laluan anda tamat. Sila tetapkan semula dengan klik \\\"Lupa kata laluan\\\"", "Your region is not allow to signup by phone": "<PERSON><PERSON><PERSON> anda tidak dibenarkan daftar melalui telefon", "password or code is incorrect": "kata laluan atau kod salah", "password or code is incorrect, you have %s remaining chances": "kata laluan atau kod salah, anda ada %s peluang lagi", "unsupported password type: %s": "jenis kata laluan tidak disokong: %s"}, "enforcer": {"the adapter: %s is not found": "penyesuai: %s tidak ditemui"}, "general": {"Failed to import groups": "Gagal import kumpulan", "Failed to import users": "Gagal import pengguna", "Missing parameter": "Parameter hilang", "Only admin user can specify user": "<PERSON><PERSON> pen<PERSON> boleh tetapkan pengguna", "Please login first": "Sila log masuk dahulu", "The organization: %s should have one application at least": "Organisasi: %s mesti ada sekurang-kura<PERSON>nya satu aplikasi", "The user: %s doesn't exist": "Pengguna: %s tidak wujud", "Wrong userId": "ID pengguna salah", "don't support captchaProvider: ": "tidak sokong penyedia captcha: ", "this operation is not allowed in demo mode": "operasi ini tidak dibenarkan dalam mod demo", "this operation requires administrator to perform": "operasi ini perlukan pentadbir untuk jalankan"}, "ldap": {"Ldap server exist": "Pelayan LDAP sudah wujud"}, "link": {"Please link first": "<PERSON><PERSON> p<PERSON> da<PERSON>u", "This application has no providers": "Aplikasi ini tiada pembekal", "This application has no providers of type": "Aplikasi ini tiada pembekal jenis", "This provider can't be unlinked": "Pembekal ini tidak boleh diputuskan", "You are not the global admin, you can't unlink other users": "Anda bukan pentadbir global, anda tidak boleh putuskan pengguna lain", "You can't unlink yourself, you are not a member of any application": "Anda tidak boleh putuskan diri sendiri, anda bukan ahli mana-mana aplikasi"}, "organization": {"Only admin can modify the %s.": "<PERSON>ya pen<PERSON>bir boleh ubah %s.", "The %s is immutable.": "%s tidak boleh diubah.", "Unknown modify rule %s.": "Peraturan ubah %s tidak diketahui.", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "<PERSON><PERSON>bahan pengguna baru ke organisasi 'built-in' (terdalam) kini dinyahdayakan. Ambil perhatian: <PERSON><PERSON><PERSON> pengguna dalam organisasi 'built-in' adalah pentadbir global dalam Casdoor. Rujuk dokumen: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. <PERSON>ka anda masih ingin mencipta pengguna untuk organisasi 'built-in', pergi ke halaman tetapan organisasi dan aktifkan pilihan 'Mempunyai kebenaran keistimewaan'."}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "Kebenaran: \\\"%s\\\" tidak wujud"}, "provider": {"Invalid application id": "ID aplikasi tidak sah", "the provider: %s does not exist": "pembekal: %s tidak wujud"}, "resource": {"User is nil for tag: avatar": "Pengguna kosong untuk tag: avatar", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "<PERSON>a pengguna atau laluan fail kosong: nama = %s, laluan = %s"}, "saml": {"Application %s not found": "Aplikasi %s tidak ditemui"}, "saml_sp": {"provider %s's category is not SAML": "kate<PERSON><PERSON> p<PERSON> %s bukan SAML"}, "service": {"Empty parameters for emailForm: %v": "Parameter kosong untuk borang emel: %v", "Invalid Email receivers: %s": "Penerima emel tidak sah: %s", "Invalid phone receivers: %s": "Penerima telefon tidak sah: %s"}, "storage": {"The objectKey: %s is not allowed": "Kunci objek: %s tidak dibenarkan", "The provider type: %s is not supported": "<PERSON><PERSON>: %s tidak disokong"}, "subscription": {"Error": "<PERSON><PERSON>"}, "token": {"Grant_type: %s is not supported in this application": "<PERSON><PERSON>em<PERSON>: %s tidak disokong dalam aplikasi ini", "Invalid application or wrong clientSecret": "Aplikasi tidak sah atau rahsia klien salah", "Invalid client_id": "ID klien tidak sah", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "URI alih: %s tiada dalam senarai URI dibenarkan", "Token not found, invalid accessToken": "Token tidak di<PERSON>ui, token akses tidak sah"}, "user": {"Display name cannot be empty": "<PERSON>a paparan tidak boleh kosong", "MFA email is enabled but email is empty": "MFA emel dibenarkan tetapi emel kosong", "MFA phone is enabled but phone number is empty": "MFA telefon dibenarkan tetapi nombor telefon kosong", "New password cannot contain blank space.": "<PERSON><PERSON> laluan baharu tidak boleh ada ruang kosong.", "the user's owner and name should not be empty": "pemilik dan nama pengguna tidak boleh kosong"}, "util": {"No application is found for userId: %s": "Tiada aplikasi ditemui untuk ID pengguna: %s", "No provider for category: %s is found for application: %s": "Tiada pembekal untuk kategori: %s dalam aplikasi: %s", "The provider: %s is not found": "Pembekal: %s tidak di<PERSON>ui"}, "verification": {"Invalid captcha provider.": "<PERSON><PERSON><PERSON> captcha tidak sah.", "Phone number is invalid in your region %s": "Nombor telefon tidak sah dalam wilayah %s", "The verification code has already been used!": "Kod pengesahan sudah digunakan!", "The verification code has not been sent yet!": "Kod pengesahan belum dihantar!", "Turing test failed.": "<PERSON><PERSON><PERSON> gagal.", "Unable to get the email modify rule.": "Tidak dapat peraturan ubah emel.", "Unable to get the phone modify rule.": "Tidak dapat peraturan ubah telefon.", "Unknown type": "<PERSON><PERSON>", "Wrong verification code!": "Kod pengesahan salah!", "You should verify your code in %d min!": "<PERSON>la sahkan kod anda dalam %d minit!", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "sila tambah pembekal SMS ke senarai \"Providers\" untuk aplikasi: %s", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "sila tambah pembekal Emel ke senarai \"Providers\" untuk aplikasi: %s", "the user does not exist, please sign up first": "pengguna tidak wujud, sila daftar dahulu"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "Sila panggil WebAuthnSigninBegin dahulu"}}