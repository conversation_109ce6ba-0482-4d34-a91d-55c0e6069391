{"account": {"Failed to add user": "הוספת משתמש נכשלה", "Get init score failed, error: %w": "קבלת ניקוד התחלתי נכשלה, שגיאה: %w", "Please sign out first": "אנא התנתק תחילה", "The application does not allow to sign up new account": "האפליקציה אינה מאפשרת הרשמה של חשבון חדש"}, "auth": {"Challenge method should be S256": "שיטת האתגר חייבת להיות S256", "DeviceCode Invalid": "<PERSON>ו<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> שגוי", "Failed to create user, user information is invalid: %s": "יצירת משתמש נכשלה, פרטי המשתמש שגויים: %s", "Failed to login in: %s": "כניסה נכשלה: %s", "Invalid token": "אסי<PERSON><PERSON><PERSON> שגוי", "State expected: %s, but got: %s": "מצב צפוי: %s, אך התקבל: %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "החשבון עבור ספק: %s ושם משתמש: %s (%s) אינו קיים ואינו מאופשר להרשמה כחשבון חדש דרך %%s, אנא השתמש בדרך אחרת להרשמה", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "החשבון עבור ספק: %s ושם משתמש: %s (%s) אינו קיים ואינו מאופשר להרשמה כחשבון חדש, אנא צור קשר עם התמיכה הטכנית", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "החשבון עבור ספק: %s ושם משתמש: %s (%s) כבר מקושר לחשבון אחר: %s (%s)", "The application: %s does not exist": "האפליקציה: %s אינה קיימת", "The login method: login with LDAP is not enabled for the application": "שיטת הכניסה: כניסה עם LDAP אינה מאופשרת לאפליקציה", "The login method: login with SMS is not enabled for the application": "שיטת הכניסה: כניסה עם SMS אינה מאופשרת לאפליקציה", "The login method: login with email is not enabled for the application": "שיטת הכניסה: כניסה עם אימייל אינה מאופשרת לאפליקציה", "The login method: login with face is not enabled for the application": "שיטת הכניסה: כניסה עם פנים אינה מאופשרת לאפליקציה", "The login method: login with password is not enabled for the application": "שיטת הכניסה: כניסה עם סיסמה אינה מאופשרת לאפליקציה", "The organization: %s does not exist": "הארגון: %s אינו קיים", "The provider: %s does not exist": "הספק: %s אינו קיים", "The provider: %s is not enabled for the application": "הספק: %s אינו מאופשר לאפליקציה", "Unauthorized operation": "פעולה לא מורשית", "Unknown authentication type (not password or provider), form = %s": "סוג אימות לא ידוע (לא סיסמה או ספק), טופס = %s", "User's tag: %s is not listed in the application's tags": "תגית המשתמש: %s אינה רשומה בתגיות האפליקציה", "UserCode Expired": "קוד משתמש פג תוקף", "UserCode Invalid": "קוד מש<PERSON><PERSON><PERSON> שגוי", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "משתמש בתשלום %s אין לו מנוי פעיל או ממתין והאפליקציה: %s אין לה תמחור ברירת מחדל", "the application for user %s is not found": "האפליקציה עבור המשתמש %s לא נמצאה", "the organization: %s is not found": "הארגון: %s לא נמצא"}, "cas": {"Service %s and %s do not match": "השירות %s ו-%s אינם תואמים"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s אינו עומד בדרישות התבנית CIDR: %s", "Affiliation cannot be blank": "השתייכות אינה יכולה להיות ריקה", "CIDR for IP: %s should not be empty": "CIDR עבור IP: %s לא צריך להיות ריק", "Default code does not match the code's matching rules": "קוד ברירת המחדל אינו תואם לכללי ההתאמה של הקוד", "DisplayName cannot be blank": "שם התצוגה אינו יכול להיות ריק", "DisplayName is not valid real name": "שם התצוגה אינו שם אמיתי תקף", "Email already exists": "האימייל כ<PERSON>ר קיים", "Email cannot be empty": "האימייל אינו יכול להיות ריק", "Email is invalid": "האימייל אינו תקף", "Empty username.": "שם משתמש ריק.", "Face data does not exist, cannot log in": "נתוני פנים אינם קיימים, לא ניתן להיכנס", "Face data mismatch": "אי התאמת נתוני פנים", "Failed to parse client IP: %s": "ניתוח כתובת ה-IP של הלקוח נכשל: %s", "FirstName cannot be blank": "שם פרטי אינו יכול להיות ריק", "Invitation code cannot be blank": "קוד הזמנה אינו יכול להיות ריק", "Invitation code exhausted": "קוד ההזמנה נוצל", "Invitation code is invalid": "קוד ההז<PERSON><PERSON>ה שגוי", "Invitation code suspended": "קוד ההזמנה הושעה", "LDAP user name or password incorrect": "שם משתמש או סיסמה של LDAP שגויים", "LastName cannot be blank": "שם משפחה אינו יכול להיות ריק", "Multiple accounts with same uid, please check your ldap server": "מספר חשבונות עם אותו uid, אנא בדוק את שרת ה-LDAP שלך", "Organization does not exist": "האר<PERSON><PERSON><PERSON> אינו קיים", "Password cannot be empty": "הסיסמה אינה יכולה להיות ריקה", "Phone already exists": "הטלפון כבר קיים", "Phone cannot be empty": "הטל<PERSON>ון אינו יכול להיות ריק", "Phone number is invalid": "מספר הטלפון אינו תקף", "Please register using the email corresponding to the invitation code": "אנא הרשם באמצעות האימייל התואם לקוד ההזמנה", "Please register using the phone  corresponding to the invitation code": "אנא הרשם באמצעות הטלפון המתאים לקוד ההזמנה", "Please register using the username corresponding to the invitation code": "אנא הרשם באמצעות שם המשתמש התואם לקוד ההזמנה", "Session outdated, please login again": "הסשן פג תוקף, אנא התחבר שוב", "The invitation code has already been used": "קוד ההזמנה כבר נוצל", "The user is forbidden to sign in, please contact the administrator": "המשתמש אסור להיכנס, אנא צור קשר עם המנהל", "The user: %s doesn't exist in LDAP server": "המשתמש: %s אינו קיים בשרת ה-LDAP", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "שם המשתמש יכול להכיל רק תווים אלפאנומריים, קווים תחתונים או מקפים, לא יכול להכיל מקפים או קווים תחתונים רצופים, ולא יכול להתחיל או להסתיים עם מקף או קו תחתון.", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "הערך \\\"%s\\\" עבור שדה החשבון \\\"%s\\\" אינו תואם ל-regex של פריט החשבון", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "הערך \\\"%s\\\" עבור שדה ההרשמה \\\"%s\\\" אינו תואם ל-regex של פריט ההרשמה של האפליקציה \\\"%s\\\"", "Username already exists": "שם המשת<PERSON><PERSON> כבר קיים", "Username cannot be an email address": "שם המשתמש לא יכול להיות כתובת אימייל", "Username cannot contain white spaces": "שם המשתמש לא יכול להכיל רווחים", "Username cannot start with a digit": "שם המשתמש לא יכול להתחיל בספרה", "Username is too long (maximum is 255 characters).": "שם המשתמש ארוך מדי (מקסימום 255 תווים).", "Username must have at least 2 characters": "שם המשת<PERSON><PERSON> חייב להכיל לפחות 2 תווים", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "שם המשתמש תומך בתבנית אימייל. כמ<PERSON> כן, שם המשתמש יכול להכיל רק תווים אלפאנומריים, קווים תחתונים או מקפים, לא יכול להכיל מקפים או קווים תחתונים רצופים, ולא יכול להתחיל או להסתיים עם מקף או קו תחתון. שים לב גם לתבנית האימייל.", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "הזנת סיסמה או קוד שגוי יותר מדי פעמים, אנא המתן %d דקות ונסה שוב", "Your IP address: %s has been banned according to the configuration of: ": "כתובת ה-IP שלך: %s נחסמה לפי התצורה של: ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "הסיסמה שלך פגה. אנא אפס את הסיסמה שלך על ידי לחיצה על \\\"שכחתי סיסמה\\\"", "Your region is not allow to signup by phone": "האזור שלך אינו מאפשר הרשמה באמצעות טלפון", "password or code is incorrect": "סיסמה או קוד שגויים", "password or code is incorrect, you have %s remaining chances": "סיסמה או קוד שגויים, יש לך %s ניסיונות נותרים", "unsupported password type: %s": "סוג סיסמה לא נתמך: %s"}, "enforcer": {"the adapter: %s is not found": "המתאם: %s לא נמצא"}, "general": {"Failed to import groups": "יבו<PERSON> קבוצות נכשל", "Failed to import users": "יבוא משתמשים נכשל", "Missing parameter": "<PERSON><PERSON><PERSON> פרמטר", "Only admin user can specify user": "רק משתמש מנהל יכול לציין משתמש", "Please login first": "אנא התחבר תחילה", "The organization: %s should have one application at least": "הארגון: %s צריך להכיל לפחות אפליקציה אחת", "The user: %s doesn't exist": "המשתמש: %s לא קיים", "Wrong userId": "מזהה משתמש שגוי", "don't support captchaProvider: ": "לא נתמך captchaP<PERSON>ider: ", "this operation is not allowed in demo mode": "פעולה זו אינה מותרת במצב הדגמה", "this operation requires administrator to perform": "פעולה זו דורשת מנהל לביצוע"}, "ldap": {"Ldap server exist": "שרת LDAP קיים"}, "link": {"Please link first": "אנא קשר תחילה", "This application has no providers": "לאפליקציה זו אין ספקים", "This application has no providers of type": "לאפליקציה זו אין ספקים מסוג", "This provider can't be unlinked": "ספק זה לא יכול להיות מנותק", "You are not the global admin, you can't unlink other users": "אינך מנהל גלובלי, אינך יכול לנתק משתמשים אחרים", "You can't unlink yourself, you are not a member of any application": "אינך יכול לנתק את עצמך, אינך חבר באף אפליקציה"}, "organization": {"Only admin can modify the %s.": "רק מנהל יכול לשנות את %s.", "The %s is immutable.": "%s הוא בלתי ניתן לשינוי.", "Unknown modify rule %s.": "כלל שינוי לא ידוע %s.", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "הוספת משתמש חדש לארגון 'מובנה' מושבת כעת. שימו לב: כל המשתמשים בארגון 'מובנה' הם מנהלים גלובליים ב-Casdoor. ראו את המסמכים: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. אם עדיין תרצו ליצור משתמש לארגון 'מובנה', עברו לדף הגדרות הארגון והפעילו את האפשרות 'יש הסכמה לזכויות'."}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "ההרשאה: \\\"%s\\\" אינה קיימת"}, "provider": {"Invalid application id": "מזהה אפליקציה שגוי", "the provider: %s does not exist": "הספק: %s אינו קיים"}, "resource": {"User is nil for tag: avatar": "המשת<PERSON><PERSON> הוא nil עבור התג: avatar", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "שם המשתמש או fullFilePath ריקים: username = %s, fullFilePath = %s"}, "saml": {"Application %s not found": "אפליקציה %s לא נמצאה"}, "saml_sp": {"provider %s's category is not SAML": "הקטגוריה של הספק %s אינה SAML"}, "service": {"Empty parameters for emailForm: %v": "פרמטרים ריקים עבור emailForm: %v", "Invalid Email receivers: %s": "מקבלי אימייל שגויים: %s", "Invalid phone receivers: %s": "מקבלי SMS שגויים: %s"}, "storage": {"The objectKey: %s is not allowed": "המפתח objectKey: %s אינו מורשה", "The provider type: %s is not supported": "סוג הספק: %s אינו נתמך"}, "subscription": {"Error": "שגיאה"}, "token": {"Grant_type: %s is not supported in this application": "סוג ההרשאה: %s אינו נתמך באפליקציה זו", "Invalid application or wrong clientSecret": "אפליק<PERSON><PERSON>ה שגויה או <PERSON><PERSON><PERSON><PERSON> שגוי", "Invalid client_id": "client_id שגוי", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "כתובת הפניה: %s לא קיימת ברשימת כתובות הפניה המורשות", "Token not found, invalid accessToken": "אסימון לא נמצא, accessToken שגוי"}, "user": {"Display name cannot be empty": "שם התצוגה אינו יכול להיות ריק", "MFA email is enabled but email is empty": "MFA דו<PERSON>\"ל מופעל אך הדוא\"ל ריק", "MFA phone is enabled but phone number is empty": "MFA טלפון מופעל אך מספר הטלפון ריק", "New password cannot contain blank space.": "הסיסמה החדשה אינה יכולה להכיל רווחים.", "the user's owner and name should not be empty": "הבעלים והשם של המשתמש אינם יכולים להיות ריקים"}, "util": {"No application is found for userId: %s": "לא נמצאה אפליקציה עבור userId: %s", "No provider for category: %s is found for application: %s": "לא נמצא ספק עבור הקטגוריה: %s עבור האפליקציה: %s", "The provider: %s is not found": "הספק: %s לא נמצא"}, "verification": {"Invalid captcha provider.": "ספ<PERSON> captcha שגוי.", "Phone number is invalid in your region %s": "מספר הטלפון אינו תקף באזורך %s", "The verification code has already been used!": "קוד האימות כבר נוצל!", "The verification code has not been sent yet!": "קוד האימות טרם נשלח!", "Turing test failed.": "<PERSON><PERSON><PERSON><PERSON> טיור<PERSON><PERSON><PERSON> נכשל.", "Unable to get the email modify rule.": "לא ניתן לקבל את כלל שינוי האימייל.", "Unable to get the phone modify rule.": "לא ניתן לקבל את כלל שינוי הטלפון.", "Unknown type": "סוג לא ידוע", "Wrong verification code!": "קוד אימות שגוי!", "You should verify your code in %d min!": "עליך לאמת את הקוד שלך תוך %d דקות!", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "אנא הוסף ספק SMS לרשימת \\\"ספקים\\\" עבור האפליקציה: %s", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "אנא הוסף ספק אימייל לרשימת \\\"ספקים\\\" עבור האפליקציה: %s", "the user does not exist, please sign up first": "המשתמש אינו קיים, אנא הרשם תחילה"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "אנא קרא ל-WebAuthnSigninBegin תחילה"}}