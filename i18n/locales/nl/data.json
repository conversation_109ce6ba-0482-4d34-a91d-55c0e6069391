{"account": {"Failed to add user": "Gebruiker toe<PERSON>n mislukt", "Get init score failed, error: %w": "Initiële score op<PERSON><PERSON> mislukt, fout: %w", "Please sign out first": "Log eerst uit", "The application does not allow to sign up new account": "Aanmelden is niet toe<PERSON>an"}, "auth": {"Challenge method should be S256": "Challenge-methode moet S256 zijn", "DeviceCode Invalid": "Ongeldige apparaatcode", "Failed to create user, user information is invalid: %s": "Aanmaken gebruiker mislukt, gegevens ongeldig: %s", "Failed to login in: %s": "Inloggen mislukt: %s", "Invalid token": "Ongeldige token", "State expected: %s, but got: %s": "Verwachtte state: %s, gekregen: %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "Gebruiker bestaat niet; aanmelden via %%s niet toeges<PERSON>, kies andere methode", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "<PERSON><PERSON><PERSON><PERSON><PERSON> best<PERSON> niet; a<PERSON><PERSON><PERSON> niet <PERSON>, neem contact op met IT", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "Account al gekoppeld aan andere gebruiker: %s (%s)", "The application: %s does not exist": "Applicatie %s bestaat niet", "The login method: login with LDAP is not enabled for the application": "LDAP-login uitgeschakeld voor deze app", "The login method: login with SMS is not enabled for the application": "SMS-login uitgeschakeld voor deze app", "The login method: login with email is not enabled for the application": "E-mail-login uitgeschakeld voor deze app", "The login method: login with face is not enabled for the application": "Face-login uitgeschakeld voor deze app", "The login method: login with password is not enabled for the application": "Wachtwoord-login uitgeschakeld voor deze app", "The organization: %s does not exist": "Organisatie %s bestaat niet", "The provider: %s does not exist": "Provider %s bestaat niet", "The provider: %s is not enabled for the application": "Provider %s uitgeschakeld voor deze app", "Unauthorized operation": "<PERSON><PERSON> toe<PERSON>e handeling", "Unknown authentication type (not password or provider), form = %s": "Onbekend authenticatietype, form = %s", "User's tag: %s is not listed in the application's tags": "Tag %s ontbreekt in app-tags", "UserCode Expired": "Gebruikerscode verlopen", "UserCode Invalid": "Ongeldige gebruikerscode", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "Betaald lid %s zonder actief abonnement en app %s heeft geen standaardprijs", "the application for user %s is not found": "App voor gebruiker %s niet gevonden", "the organization: %s is not found": "Organisatie %s niet gevonden"}, "cas": {"Service %s and %s do not match": "Services %s en %s komen niet overeen"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s voldoet niet aan CIDR-formaat: %s", "Affiliation cannot be blank": "Affiliatie is verplicht", "CIDR for IP: %s should not be empty": "CIDR voor IP %s mag niet leeg zijn", "Default code does not match the code's matching rules": "Standaardcode komt niet overeen met regels", "DisplayName cannot be blank": "Weergavenaam is verplicht", "DisplayName is not valid real name": "<PERSON>n geldige echte naam", "Email already exists": "E-mail bestaat al", "Email cannot be empty": "E-mail is verplicht", "Email is invalid": "Ongeldig e-mailadres", "Empty username.": "Gebruikersnaam ontbreekt", "Face data does not exist, cannot log in": "<PERSON><PERSON> face-g<PERSON><PERSON><PERSON>, inloggen niet mogelijk", "Face data mismatch": "Face-g<PERSON><PERSON><PERSON> komen niet overeen", "Failed to parse client IP: %s": "IP parsen mislukt: %s", "FirstName cannot be blank": "Voornaam is verplicht", "Invitation code cannot be blank": "Uitnodigingscode is verplicht", "Invitation code exhausted": "Uitnodigingscode volledig gebruikt", "Invitation code is invalid": "Ongeldige uitnodigingscode", "Invitation code suspended": "Uitnodigingscode opgeschort", "LDAP user name or password incorrect": "LDAP-g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of wachtwoord onjuist", "LastName cannot be blank": "Achternaam is verplicht", "Multiple accounts with same uid, please check your ldap server": "Meerdere accounts met <PERSON><PERSON><PERSON> <PERSON><PERSON>, controleer LDAP-server", "Organization does not exist": "Organisatie bestaat niet", "Password cannot be empty": "Wachtwoord is verplicht", "Phone already exists": "Telefoonnummer bestaat al", "Phone cannot be empty": "Telefoonnummer is verplicht", "Phone number is invalid": "Ongeldig telefoonnummer", "Please register using the email corresponding to the invitation code": "<PERSON><PERSON><PERSON> met het e-mailadres dat bij de code hoort", "Please register using the phone  corresponding to the invitation code": "<PERSON><PERSON><PERSON> met het nummer dat bij de code hoort", "Please register using the username corresponding to the invitation code": "<PERSON><PERSON><PERSON> met de geb<PERSON>ikers<PERSON>am die bij de code hoort", "Session outdated, please login again": "<PERSON><PERSON> verlopen, log opnieuw in", "The invitation code has already been used": "Code al gebruikt", "The user is forbidden to sign in, please contact the administrator": "Inlog<PERSON> verboden, neem contact op met beheerder", "The user: %s doesn't exist in LDAP server": "Gebruiker %s ontbreekt in LDAP", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "Gebruikersnaam: alleen letters, cijfers, _ of -; geen dubbele streepjes/underscores; mag niet beginnen/eindigen met streepje of underscore.", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "Waarde \"%s\" voor veld \"%s\" voldoet niet aan regex", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "Waarde \"%s\" voor aanmeldveld \"%s\" voldoet niet aan regex van app \"%s\"", "Username already exists": "Gebruikers<PERSON><PERSON> bestaat al", "Username cannot be an email address": "Gebruikersnaam mag geen e-mailadres zijn", "Username cannot contain white spaces": "Gebruikersnaam mag geen spaties bevatten", "Username cannot start with a digit": "Gebruikersnaam mag niet met cij<PERSON> beginnen", "Username is too long (maximum is 255 characters).": "Gebruikersnaam te lang (max 255 tekens)", "Username must have at least 2 characters": "Minimaal 2 tekens", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "Gebruikersnaam kan e-mail zijn; alleen letters, cijfers, _ of -; geen dubbele streepjes/underscores; mag niet beginnen/eindigen met streepje of underscore.", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "Te vaak fout wachtwoord/code, wacht %d minuten", "Your IP address: %s has been banned according to the configuration of: ": "IP-adres %s geblokkeerd volgens configuratie: ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "Wachtwoord verlopen; klik op \"Wachtwoord vergeten\"", "Your region is not allow to signup by phone": "Registratie per telefoon niet toe<PERSON> in jouw regio", "password or code is incorrect": "<PERSON><PERSON><PERSON><PERSON> wachtwoord of code", "password or code is incorrect, you have %s remaining chances": "Verkeerd wachtwoord/code, nog %s pogingen", "unsupported password type: %s": "Niet-ondersteund wachtwoordtype: %s"}, "enforcer": {"the adapter: %s is not found": "Adapter %s niet gevonden"}, "general": {"Failed to import groups": "Groepen importeren mislukt", "Failed to import users": "Gebruikers importeren mislukt", "Missing parameter": "Parameter ontbreekt", "Only admin user can specify user": "<PERSON><PERSON> beheerder mag gebruiker opgeven", "Please login first": "Log eerst in", "The organization: %s should have one application at least": "Organisatie %s moet minstens één app hebben", "The user: %s doesn't exist": "Gebruiker %s bestaat niet", "Wrong userId": "Verkeerde userId", "don't support captchaProvider: ": "Captcha-provider niet ondersteund: ", "this operation is not allowed in demo mode": "<PERSON>ing niet <PERSON> in demo-modus", "this operation requires administrator to perform": "<PERSON><PERSON> beheerder kan deze handeling uit<PERSON>eren"}, "ldap": {"Ldap server exist": "LDAP-server bestaat al"}, "link": {"Please link first": "<PERSON><PERSON>", "This application has no providers": "App heeft geen providers", "This application has no providers of type": "App heeft geen providers van dit type", "This provider can't be unlinked": "Provider kan niet ontko<PERSON>d worden", "You are not the global admin, you can't unlink other users": "Je bent geen globale beheerder, kunt anderen niet ont<PERSON>en", "You can't unlink yourself, you are not a member of any application": "Kan jez<PERSON> niet on<PERSON>; geen lid van een app"}, "organization": {"Only admin can modify the %s.": "<PERSON>een be<PERSON>r kan %s wijzigen.", "The %s is immutable.": "%s kan niet gewijzigd worden.", "Unknown modify rule %s.": "Onbekende wijzigingsregel %s.", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "Het toevoegen van een nieuwe gebruiker aan de 'built-in' (ingebouwde) organisatie is momenteel uitgeschakeld. Let op: Alle gebruikers in de 'built-in' organisatie zijn globale beheerders in Casdoor. Raadpleeg de documentatie: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. Als u toch een gebruiker wilt maken voor de 'built-in' organisatie, ga naar de instellingenpagina van de organisatie en schakel de optie 'Heeft bevoegdheidsgoedkeuring' in."}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "Permissie \"%s\" bestaat niet"}, "provider": {"Invalid application id": "Ongeldige app-id", "the provider: %s does not exist": "Provider %s bestaat niet"}, "resource": {"User is nil for tag: avatar": "Gebruiker ontbreekt voor avatar-tag", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "Gebruikersnaam of bestandspad leeg: %s / %s"}, "saml": {"Application %s not found": "App %s niet gevonden"}, "saml_sp": {"provider %s's category is not SAML": "Provider %s is geen SAML-type"}, "service": {"Empty parameters for emailForm: %v": "Lege parameters voor e-mailformulier: %v", "Invalid Email receivers: %s": "Ongeldige e-mailontvangers: %s", "Invalid phone receivers: %s": "Ongeldige telefoonontvangers: %s"}, "storage": {"The objectKey: %s is not allowed": "ObjectKey %s niet toe<PERSON>an", "The provider type: %s is not supported": "Providertype %s niet on<PERSON>teund"}, "subscription": {"Error": "Fout"}, "token": {"Grant_type: %s is not supported in this application": "Grant_type %s wordt niet ondersteund", "Invalid application or wrong clientSecret": "Ongeldige app of verkeerde clientSecret", "Invalid client_id": "Ongeldige client_id", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "Redirect-URI %s staat niet op de toegestane lijst", "Token not found, invalid accessToken": "Token niet gevonden; ongeldige accessToken"}, "user": {"Display name cannot be empty": "Weergavenaam is verplicht", "MFA email is enabled but email is empty": "MFA-e-mail ingeschakeld maar e-mailadres leeg", "MFA phone is enabled but phone number is empty": "MFA-telefoon ingeschakeld maar nummer leeg", "New password cannot contain blank space.": "<PERSON><PERSON><PERSON> wachtwoord mag geen spaties bevatten", "the user's owner and name should not be empty": "Eigenaar en naam van gebruiker mogen niet leeg zijn"}, "util": {"No application is found for userId: %s": "Geen app gevonden voor userId %s", "No provider for category: %s is found for application: %s": "Geen provider voor categorie %s in app %s", "The provider: %s is not found": "Provider %s niet gevonden"}, "verification": {"Invalid captcha provider.": "Ongeldige captcha-provider", "Phone number is invalid in your region %s": "Telefoonnummer ongeldig in regio %s", "The verification code has already been used!": "Verificatiecode al gebruikt!", "The verification code has not been sent yet!": "Verificatiecode nog niet verzonden!", "Turing test failed.": "Turing-test mislukt", "Unable to get the email modify rule.": "Kan e-mail-wijzigingsregel niet ophalen", "Unable to get the phone modify rule.": "Kan telefoon-wijzigingsregel niet ophalen", "Unknown type": "Onbekend type", "Wrong verification code!": "Verkeerde verificatiecode!", "You should verify your code in %d min!": "Verifieer binnen %d min!", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "Voeg een SMS-provider toe aan de Providers-lijst van app %s", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "Voeg een e-mailprovider toe aan de Providers-lijst van app %s", "the user does not exist, please sign up first": "<PERSON><PERSON><PERSON><PERSON><PERSON> bestaat niet; meld je eerst aan"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "<PERSON><PERSON> e<PERSON>t WebAuthnSigninBegin aan"}}