{"account": {"Failed to add user": "Échec d'ajout d'utilisateur", "Get init score failed, error: %w": "Obtention du score initiale échouée, erreur : %w", "Please sign out first": "Veuillez vous déconnecter en premier", "The application does not allow to sign up new account": "L'application ne permet pas de créer un nouveau compte"}, "auth": {"Challenge method should be S256": "La méthode de défi doit être S256", "DeviceCode Invalid": "Code appareil invalide", "Failed to create user, user information is invalid: %s": "Échec de la création de l'utilisateur, les informations utilisateur sont invalides : %s", "Failed to login in: %s": "Échec de la connexion : %s", "Invalid token": "<PERSON><PERSON> invalide", "State expected: %s, but got: %s": "État attendu : %s, mais obtenu : %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "Le compte pour le fournisseur : %s et le nom d'utilisateur : %s (%s) n'existe pas et n'est pas autorisé à s'inscrire en tant que nouveau compte via %%s, veuillez utiliser une autre méthode pour vous inscrire", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "Le compte pour le fournisseur : %s et le nom d'utilisateur : %s (%s) n'existe pas et n'est pas autorisé à s'inscrire comme nouveau compte, veuillez contacter votre support informatique", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "Le compte du fournisseur : %s et le nom d'utilisateur : %s (%s) sont déjà liés à un autre compte : %s (%s)", "The application: %s does not exist": "L'application : %s n'existe pas", "The login method: login with LDAP is not enabled for the application": "La méthode de connexion : connexion LDAP n'est pas activée pour l'application", "The login method: login with SMS is not enabled for the application": "La méthode de connexion : connexion par SMS n'est pas activée pour l'application", "The login method: login with email is not enabled for the application": "La méthode de connexion : connexion par e-mail n'est pas activée pour l'application", "The login method: login with face is not enabled for the application": "La méthode de connexion : connexion par visage n'est pas activée pour l'application", "The login method: login with password is not enabled for the application": "La méthode de connexion : connexion avec mot de passe n'est pas activée pour l'application", "The organization: %s does not exist": "L'organisation : %s n'existe pas", "The provider: %s does not exist": "Le fournisseur : %s n'existe pas", "The provider: %s is not enabled for the application": "Le fournisseur :%s n'est pas activé pour l'application", "Unauthorized operation": "Opération non autorisée", "Unknown authentication type (not password or provider), form = %s": "Type d'authentification inconnu (pas de mot de passe ou de fournisseur), formulaire = %s", "User's tag: %s is not listed in the application's tags": "Le tag de l'utilisateur : %s n'est pas répertorié dans les tags de l'application", "UserCode Expired": "Code utilisateur expiré", "UserCode Invalid": "Code utilisateur invalide", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "L'utilisateur payant %s n'a pas d'abonnement actif ou en attente et l'application : %s n'a pas de tarification par défaut", "the application for user %s is not found": "L'application pour l'utilisateur %s est introuvable", "the organization: %s is not found": "L'organisation : %s est introuvable"}, "cas": {"Service %s and %s do not match": "Les services %s et %s ne correspondent pas"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s ne respecte pas les exigences du format CIDR : %s", "Affiliation cannot be blank": "Affiliation ne peut pas être vide", "CIDR for IP: %s should not be empty": "Le CIDR pour l'IP : %s ne doit pas être vide", "Default code does not match the code's matching rules": "Le code par défaut ne correspond pas aux règles de correspondance du code", "DisplayName cannot be blank": "Le nom d'affichage ne peut pas être vide", "DisplayName is not valid real name": "DisplayName n'est pas un nom réel valide", "Email already exists": "E-mail déjà existant", "Email cannot be empty": "L'e-mail ne peut pas être vide", "Email is invalid": "L'adresse e-mail est invalide", "Empty username.": "Nom d'utilisateur vide.", "Face data does not exist, cannot log in": "Les données faciales n'existent pas, connexion impossible", "Face data mismatch": "Données faciales incorrectes", "Failed to parse client IP: %s": "Échec de l'analyse de l'IP client : %s", "FirstName cannot be blank": "Le prénom ne peut pas être laissé vide", "Invitation code cannot be blank": "Le code d'invitation ne peut pas être vide", "Invitation code exhausted": "Code d'invitation épuisé", "Invitation code is invalid": "Code d'invitation invalide", "Invitation code suspended": "Code d'invitation suspendu", "LDAP user name or password incorrect": "Nom d'utilisateur ou mot de passe LDAP incorrect", "LastName cannot be blank": "Le nom de famille ne peut pas être vide", "Multiple accounts with same uid, please check your ldap server": "Plusieurs comptes avec le même identifiant d'utilisateur, veuillez vérifier votre serveur LDAP", "Organization does not exist": "L'organisation n'existe pas", "Password cannot be empty": "Le mot de passe ne peut pas être vide", "Phone already exists": "Le téléphone existe déjà", "Phone cannot be empty": "Le téléphone ne peut pas être vide", "Phone number is invalid": "Le numéro de téléphone est invalide", "Please register using the email corresponding to the invitation code": "Veuillez vous inscrire avec l'e-mail correspondant au code d'invitation", "Please register using the phone  corresponding to the invitation code": "Veuillez vous inscrire avec le téléphone correspondant au code d'invitation", "Please register using the username corresponding to the invitation code": "Veuillez vous inscrire avec le nom d'utilisateur correspondant au code d'invitation", "Session outdated, please login again": "Session expirée, veuil<PERSON>z vous connecter à nouveau", "The invitation code has already been used": "Le code d'invitation a déjà été utilisé", "The user is forbidden to sign in, please contact the administrator": "L'utilisateur est interdit de se connecter, veuillez contacter l'administrateur", "The user: %s doesn't exist in LDAP server": "L'utilisateur : %s n'existe pas sur le serveur LDAP", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "Le nom d'utilisateur ne peut contenir que des caractères alphanumériques, des traits soulignés ou des tirets, ne peut pas avoir de tirets ou de traits soulignés consécutifs et ne peut pas commencer ou se terminer par un tiret ou un trait souligné.", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "La valeur \\\"%s\\\" pour le champ de compte \\\"%s\\\" ne correspond pas à l'expression régulière de l'élément de compte", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "La valeur \\\"%s\\\" pour le champ d'inscription \\\"%s\\\" ne correspond pas à l'expression régulière de l'élément d'inscription de l'application \\\"%s\\\"", "Username already exists": "Nom d'utilisateur existe déjà", "Username cannot be an email address": "Nom d'utilisateur ne peut pas être une adresse e-mail", "Username cannot contain white spaces": "Nom d'utilisateur ne peut pas contenir d'espaces blancs", "Username cannot start with a digit": "Nom d'utilisateur ne peut pas commencer par un chiffre", "Username is too long (maximum is 255 characters).": "Nom d'utilisateur est trop long (maximum de 255 caractères).", "Username must have at least 2 characters": "Le nom d'utilisateur doit comporter au moins 2 caractères", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "Le nom d'utilisateur prend en charge le format e-mail. De plus, il ne peut contenir que des caractères alphanumériques, des tirets bas ou des traits d'union, ne peut pas avoir de traits d'union ou de tirets bas consécutifs, et ne peut pas commencer ou se terminer par un trait d'union ou un tiret bas. Faites également attention au format de l'e-mail.", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "Vous avez entré le mauvais mot de passe ou code plusieurs fois, veuillez attendre %d minutes et réessayer", "Your IP address: %s has been banned according to the configuration of: ": "Votre adresse IP : %s a été bannie selon la configuration de : ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "Votre mot de passe a expiré. Veuillez le réinitialiser en cliquant sur \\\"Mot de passe oublié\\\"", "Your region is not allow to signup by phone": "Votre région n'est pas autorisée à s'inscrire par téléphone", "password or code is incorrect": "mot de passe ou code incorrect", "password or code is incorrect, you have %s remaining chances": "Le mot de passe ou le code est incorrect, il vous reste %s chances", "unsupported password type: %s": "Type de mot de passe non pris en charge : %s"}, "enforcer": {"the adapter: %s is not found": "l'adaptateur : %s est introuvable"}, "general": {"Failed to import groups": "Échec de l'importation des groupes", "Failed to import users": "Échec de l'importation des utilisateurs", "Missing parameter": "<PERSON><PERSON><PERSON><PERSON>", "Only admin user can specify user": "Seul un administrateur peut désigner un utilisateur", "Please login first": "Veuillez d'abord vous connecter", "The organization: %s should have one application at least": "L'organisation : %s doit avoir au moins une application", "The user: %s doesn't exist": "L'utilisateur : %s n'existe pas", "Wrong userId": "ID utilisateur incorrect", "don't support captchaProvider: ": "ne prend pas en charge cap<PERSON><PERSON><PERSON><PERSON><PERSON>: ", "this operation is not allowed in demo mode": "cette opération n'est pas autorisée en mode démo", "this operation requires administrator to perform": "cette opération nécessite un administrateur pour être effectuée"}, "ldap": {"Ldap server exist": "Le serveur LDAP existe"}, "link": {"Please link first": "Veuillez d'abord faire le lien", "This application has no providers": "Cette application n'a aucun fournisseur", "This application has no providers of type": "Cette application ne dispose d'aucun fournisseur de type", "This provider can't be unlinked": "Ce fournis<PERSON>ur ne peut pas être dissocié", "You are not the global admin, you can't unlink other users": "Vous n'êtes pas l'administrateur global, vous ne pouvez pas détacher d'autres utilisateurs", "You can't unlink yourself, you are not a member of any application": "Vous ne pouvez pas vous d<PERSON>oli<PERSON>, car vous n'êtes membre d'aucune application"}, "organization": {"Only admin can modify the %s.": "Seul l'administrateur peut modifier le %s.", "The %s is immutable.": "Le %s est immuable.", "Unknown modify rule %s.": "Règle de modification inconnue %s.", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "L'ajout d'un nouvel utilisateur à l'organisation « built-in » (intégrée) est actuellement désactivé. Veuillez noter : Tous les utilisateurs de l'organisation « built-in » sont des administrateurs globaux dans Casdoor. Consulter la documentation : https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. Si vous souhaitez encore créer un utilisateur pour l'organisation « built-in », accédez à la page des paramètres de l'organisation et activez l'option « A le consentement aux privilèges »."}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "La permission : \\\"%s\\\" n'existe pas"}, "provider": {"Invalid application id": "Identifiant d'application invalide", "the provider: %s does not exist": "Le fournisseur : %s n'existe pas"}, "resource": {"User is nil for tag: avatar": "L'utilisateur est nul pour la balise : avatar", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "Nom d'utilisateur ou chemin complet du fichier est vide : nom d'utilisateur = %s, chemin complet du fichier = %s"}, "saml": {"Application %s not found": "L'application %s n'a pas été trouvée"}, "saml_sp": {"provider %s's category is not SAML": "La catégorie du fournisseur %s n'est pas SAML"}, "service": {"Empty parameters for emailForm: %v": "Paramètres vides pour emailForm : %v", "Invalid Email receivers: %s": "Destinataires d'e-mail invalides : %s", "Invalid phone receivers: %s": "Destinataires de téléphone invalide : %s"}, "storage": {"The objectKey: %s is not allowed": "La clé d'objet : %s n'est pas autorisée", "The provider type: %s is not supported": "Le type de fournisseur : %s n'est pas pris en charge"}, "subscription": {"Error": "<PERSON><PERSON><PERSON>"}, "token": {"Grant_type: %s is not supported in this application": "Type_de_subvention : %s n'est pas pris en charge dans cette application", "Invalid application or wrong clientSecret": "Application invalide ou client<PERSON><PERSON><PERSON> incorrect", "Invalid client_id": "Identifiant de client invalide", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "URI de redirection: %s n'existe pas dans la liste des URI de redirection autorisés", "Token not found, invalid accessToken": "<PERSON>on non trouvé, accessToken invalide"}, "user": {"Display name cannot be empty": "Le nom d'affichage ne peut pas être vide", "MFA email is enabled but email is empty": "L'authentification MFA par e-mail est activée mais l'e-mail est vide", "MFA phone is enabled but phone number is empty": "L'authentification MFA par téléphone est activée mais le numéro de téléphone est vide", "New password cannot contain blank space.": "Le nouveau mot de passe ne peut pas contenir d'espace.", "the user's owner and name should not be empty": "le propriétaire et le nom de l'utilisateur ne doivent pas être vides"}, "util": {"No application is found for userId: %s": "Aucune application n'a été trouvée pour l'identifiant d'utilisateur : %s", "No provider for category: %s is found for application: %s": "Aucun fournisseur pour la catégorie: %s n'est trouvé pour l'application: %s", "The provider: %s is not found": "Le fournisseur : %s n'a pas été trouvé"}, "verification": {"Invalid captcha provider.": "Fournis<PERSON><PERSON> de captcha invalide.", "Phone number is invalid in your region %s": "Le numéro de téléphone n'est pas valide dans votre région %s", "The verification code has already been used!": "Le code de vérification a déjà été utilisé !", "The verification code has not been sent yet!": "Le code de vérification n'a pas encore été envoyé !", "Turing test failed.": "Le test de Turing a échoué.", "Unable to get the email modify rule.": "Incapable d'obtenir la règle de modification de courriel.", "Unable to get the phone modify rule.": "Impossible d'obtenir la règle de modification de téléphone.", "Unknown type": "Type inconnu", "Wrong verification code!": "Mauvais code de vérification !", "You should verify your code in %d min!": "Vous devriez vérifier votre code en %d min !", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "veuillez ajouter un fournisseur SMS à la liste \\\"Providers\\\" pour l'application : %s", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "veuillez ajouter un fournisseur d'e-mail à la liste \\\"Providers\\\" pour l'application : %s", "the user does not exist, please sign up first": "L'utilisateur n'existe pas, veuillez vous inscrire d'abord"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "Veuillez d'abord appeler WebAuthnSigninBegin"}}