{"account": {"Failed to add user": "Käyttäjä<PERSON> lis<PERSON> epäonnist<PERSON>", "Get init score failed, error: %w": "Alkupisteen haku epäon<PERSON>, virhe: %w", "Please sign out first": "<PERSON><PERSON><PERSON><PERSON><PERSON> en<PERSON> ul<PERSON>", "The application does not allow to sign up new account": "Sovellus ei salli uuden tilin luomista"}, "auth": {"Challenge method should be S256": "Haasteen metodin tulee olla S256", "DeviceCode Invalid": "DeviceCode virheellinen", "Failed to create user, user information is invalid: %s": "Käyttäjä<PERSON> luonti e<PERSON>, käyttäjätiedot ovat virheelliset: %s", "Failed to login in: %s": "Sisäänkirjautuminen epäonnistui: %s", "Invalid token": "<PERSON><PERSON><PERSON><PERSON><PERSON> token", "State expected: %s, but got: %s": "Odotettiin tilaa: %s, mutta saatiin: %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "Tiliä providerille: %s ja käyttäjälle: %s (%s) ei ole olemassa, eikä sitä voi rekisteröidä uutena tilinä kautta %%s, käytä toista tapaa rekisteröityä", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "Tiliä providerille: %s ja käyttäjälle: %s (%s) ei ole olemassa, eikä sitä voi rekisteröidä uutena tilinä, ota yhteyttä IT-tukeen", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "Tili providerille: %s ja käyttäjälle: %s (%s) on jo linkitetty toiseen tiliin: %s (%s)", "The application: %s does not exist": "Sovellus: %s ei ole olemassa", "The login method: login with LDAP is not enabled for the application": "Kirjautumistapa: kirjautuminen LDAP:n kautta ei ole käytössä sovelluksessa", "The login method: login with SMS is not enabled for the application": "Kirjautumistapa: kirjautuminen SMS:n kautta ei ole käytössä sovelluksessa", "The login method: login with email is not enabled for the application": "Kirjautumistapa: kirjautuminen sähköpostin kautta ei ole käytössä sovelluksessa", "The login method: login with face is not enabled for the application": "Kirjautumistapa: kirjautuminen kasvotunnistuksella ei ole käytössä sovelluksessa", "The login method: login with password is not enabled for the application": "Kirjautumistapa: kirjautuminen salasanalla ei ole käytössä sovelluksessa", "The organization: %s does not exist": "Organisaatio: %s ei ole olemassa", "The provider: %s does not exist": "Provider: %s ei ole olemassa", "The provider: %s is not enabled for the application": "Provider: %s ei ole käytössä sovelluksessa", "Unauthorized operation": "<PERSON><PERSON><PERSON> toiminto", "Unknown authentication type (not password or provider), form = %s": "Tuntematon todennustyyppi (ei salasana tai provider), form = %s", "User's tag: %s is not listed in the application's tags": "Käyttäjän tagi: %s ei ole listattu sovelluksen tageissa", "UserCode Expired": "UserCode vanhentunut", "UserCode Invalid": "UserCode virheellinen", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "maksettu-käyttäjä %s ei ole aktiivista tai odottavaa tilausta ja sovellus: %s ei ole oletushinnoittelua", "the application for user %s is not found": "sovellusta käyttäjälle %s ei löydy", "the organization: %s is not found": "organisaatiota: %s ei löydy"}, "cas": {"Service %s and %s do not match": "Palvelu %s ja %s eivät täsmää"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s ei täytä CIDR-muodon vaatimuksia: %s", "Affiliation cannot be blank": "Affiliaatio ei voi olla tyhjä", "CIDR for IP: %s should not be empty": "CIDR IP:lle: %s ei saa olla tyhjä", "Default code does not match the code's matching rules": "Oletuskoodi ei täsmää koodin täsmäyssääntöihin", "DisplayName cannot be blank": "Näyttönimi ei voi olla tyhjä", "DisplayName is not valid real name": "Näyttönimi ei ole kelvollinen oikea nimi", "Email already exists": "Sähköposti on jo olemassa", "Email cannot be empty": "Sähköposti ei voi olla tyhjä", "Email is invalid": "Sähköposti on virheellinen", "Empty username.": "Tyhjä k<PERSON>täjänimi.", "Face data does not exist, cannot log in": "Kasvodataa ei ole o<PERSON>, ei voi kirjautua", "Face data mismatch": "Kasvodata ei täsmää", "Failed to parse client IP: %s": "Client IP:n jäsentäminen epäonnistui: %s", "FirstName cannot be blank": "Etunimi ei voi olla tyhjä", "Invitation code cannot be blank": "Kutsukoodi ei voi olla tyhjä", "Invitation code exhausted": "<PERSON><PERSON><PERSON><PERSON><PERSON> on k<PERSON><PERSON><PERSON> loppuun", "Invitation code is invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> on virheellinen", "Invitation code suspended": "<PERSON><PERSON><PERSON><PERSON><PERSON> on keskeytetty", "LDAP user name or password incorrect": "LDAP-käyttäjänimi tai salasana on virheellinen", "LastName cannot be blank": "Sukunimi ei voi olla tyhjä", "Multiple accounts with same uid, please check your ldap server": "<PERSON><PERSON> tilejä samalla uid:llä, tarkista ldap-palvelimesi", "Organization does not exist": "Organisaatiota ei ole olemassa", "Password cannot be empty": "Salasana ei voi olla tyhjä", "Phone already exists": "<PERSON><PERSON><PERSON><PERSON><PERSON> on jo olemassa", "Phone cannot be empty": "Puhelinnumero ei voi olla tyhjä", "Phone number is invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> on virheellinen", "Please register using the email corresponding to the invitation code": "Rekisteröidy käyttämällä kutsukoodiin vastaavaa sähköpostia", "Please register using the phone  corresponding to the invitation code": "Rekisteröidy k<PERSON>yttämällä kutsukoodiin vastaavaa puhelinnumeroa", "Please register using the username corresponding to the invitation code": "Rekisteröidy käyttämällä kutsukoodiin vastaavaa käyttäjänimeä", "Session outdated, please login again": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON>", "The invitation code has already been used": "<PERSON><PERSON><PERSON><PERSON><PERSON> on jo k<PERSON><PERSON>", "The user is forbidden to sign in, please contact the administrator": "Käyttäjän kirjautuminen on estetty, ota yhteyttä ylläpitäjään", "The user: %s doesn't exist in LDAP server": "Käyttäjä: %s ei ole olemassa LDAP-palvelimessa", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "Käyttäjänimi voi sisältää vain alfanumeerisia merkkejä, alaviivoja tai tavuviivoja, ei voi sisältää peräkkäisiä tavuviivoja tai alaviivoja, eikä voi alkaa tai loppua tavuviivalla tai alaviivalla.", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "Arvo \\\"%s\\\" tili-kentälle \\\"%s\\\" ei täsmää tili-alkion regexiin", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "Arvo \\\"%s\\\" rekisteröitymiskentälle \\\"%s\\\" ei täsmää sovelluksen \\\"%s\\\" rekisteröitymiskentän regexiin", "Username already exists": "K<PERSON>yttäjänimi on jo olemassa", "Username cannot be an email address": "Käyttäjänimi ei voi olla sähköpostiosoite", "Username cannot contain white spaces": "Käyttäjänimi ei voi sisältää välilyöntejä", "Username cannot start with a digit": "Käyttäjänimi ei voi alkaa numerolla", "Username is too long (maximum is 255 characters).": "<PERSON><PERSON><PERSON><PERSON><PERSON>j<PERSON><PERSON><PERSON> on liian pitkä (enintään 255 merkkiä).", "Username must have at least 2 characters": "Käyttäjänimessä on oltava vähintään 2 merkkiä", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "Käyttäjänimi tukee sähköpostimuotoa. Lisäksi käyttäjänimi voi sisältää vain alfanumeerisia merkkejä, alaviivoja tai tavuviivoja, ei voi sisältää peräkkäisiä tavuviivoja tai alaviivoja, eikä voi alkaa tai loppua tavuviivalla tai alaviivalla. Huomioi myös sähköpostimuoto.", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "<PERSON>t s<PERSON>ttänyt väärän salasanan tai koodin liian monta kertaa, odota %d minuuttia ja yritä uudelleen", "Your IP address: %s has been banned according to the configuration of: ": "IP-osoitteesi: %s on estetty asetuksen mukaan: ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "Salasanasi on vanhentunut. Nollaa salasanasi klik<PERSON>lla \\\"Unohdin salasanan\\\"", "Your region is not allow to signup by phone": "Alueellasi ei ole sallittua rekisteröityä puhelimella", "password or code is incorrect": "salasana tai koodi on vir<PERSON><PERSON><PERSON>", "password or code is incorrect, you have %s remaining chances": "salasana tai koodi on vir<PERSON><PERSON><PERSON>, sin<PERSON><PERSON> on %s y<PERSON><PERSON><PERSON> j<PERSON>ä", "unsupported password type: %s": "ei-tuettu salasanatyyppi: %s"}, "enforcer": {"the adapter: %s is not found": "adapteria: %s ei löydy"}, "general": {"Failed to import groups": "<PERSON><PERSON><PERSON><PERSON> tuonti ep<PERSON>", "Failed to import users": "Käyttäji<PERSON> tuonti ep<PERSON>", "Missing parameter": "<PERSON><PERSON><PERSON> pu<PERSON>u", "Only admin user can specify user": "Vain yll<PERSON>pitäjä voi määrittää käyttäjän", "Please login first": "<PERSON><PERSON><PERSON><PERSON><PERSON> ensin si<PERSON>", "The organization: %s should have one application at least": "Organisaatiolla: %s on oltava vähintään yksi sovellus", "The user: %s doesn't exist": "Käyttäjää: %s ei ole olemassa", "Wrong userId": "Väärä userId", "don't support captchaProvider: ": "ei tueta cap<PERSON><PERSON>: ", "this operation is not allowed in demo mode": "tämä toiminto ei ole sallittu demo-tilassa", "this operation requires administrator to perform": "tämä toiminto vaatii ylläpitäjän suoritta<PERSON>a"}, "ldap": {"Ldap server exist": "LDAP-palvelin on olemassa"}, "link": {"Please link first": "<PERSON><PERSON><PERSON> en<PERSON>", "This application has no providers": "Tällä sovelluksella ei ole providereita", "This application has no providers of type": "T<PERSON><PERSON><PERSON> sovelluk<PERSON>la ei ole tämän tyyppisiä providereita", "This provider can't be unlinked": "Tätä provideria ei voi irrottaa", "You are not the global admin, you can't unlink other users": "Et ole globaali yll<PERSON>pit<PERSON>j<PERSON>, et voi irrottaa muita käyttäjiä", "You can't unlink yourself, you are not a member of any application": "Et voi irrottaa itseäsi, et ole mink<PERSON>än sovelluksen jäsen"}, "organization": {"Only admin can modify the %s.": "Vain y<PERSON>äpitäjä voi muokata %s.", "The %s is immutable.": "%s on muuttumaton.", "Unknown modify rule %s.": "Tuntematon muokkaussääntö %s.", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "<PERSON>uden käyttäjän lisääminen 'built-in'-organisaatioon on tällä hetkell<PERSON> poistettu käytöstä. Huomioi: Kaikki 'built-in'-organisaation käyttäjät ovat Casdoorin globaaleja ylläpitäjiä. Katso dokumentaatio: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. Jos haluat silti luoda käyttäjän 'built-in'-organisaatiolle, siirry organisaation asetussivulle ja käytä käyttöön vaihtoehdon 'On oikeuksien suostumus'."}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "Oikeutta: \\\"%s\\\" ei ole olemassa"}, "provider": {"Invalid application id": "<PERSON><PERSON><PERSON><PERSON><PERSON> so<PERSON> id", "the provider: %s does not exist": "provideria: %s ei ole olemassa"}, "resource": {"User is nil for tag: avatar": "<PERSON><PERSON><PERSON>täjä on nil tagille: avatar", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "Käyttäjänimi tai fullFilePath on tyhjä: username = %s, fullFilePath = %s"}, "saml": {"Application %s not found": "Sovellusta %s ei löydy"}, "saml_sp": {"provider %s's category is not SAML": "provider %s:n kategoria ei ole SAML"}, "service": {"Empty parameters for emailForm: %v": "Tyhjät parametrit emailFormille: %v", "Invalid Email receivers: %s": "Virheelliset sähköpostivastaanottajat: %s", "Invalid phone receivers: %s": "Virheelliset puhelinvastaanottajat: %s"}, "storage": {"The objectKey: %s is not allowed": "objectKey: %s ei ole sallittu", "The provider type: %s is not supported": "Provider-tyyppiä: %s ei tueta"}, "subscription": {"Error": "<PERSON><PERSON><PERSON>"}, "token": {"Grant_type: %s is not supported in this application": "Grant_type: %s ei ole tuettu tässä sovelluksessa", "Invalid application or wrong clientSecret": "Virheellinen sovellus tai väärä clientSecret", "Invalid client_id": "Virheellinen client_id", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "Uudelleenohjaus-URI: %s ei ole sallittujen uudelleenohjaus-URI-listassa", "Token not found, invalid accessToken": "Tokenia ei <PERSON>, virheellinen accessToken"}, "user": {"Display name cannot be empty": "Näyttönimi ei voi olla tyhjä", "MFA email is enabled but email is empty": "MFA-sähköposti on käytössä, mutta sähköposti on tyhjä", "MFA phone is enabled but phone number is empty": "MFA-<PERSON><PERSON><PERSON> on käytössä, mutta pu<PERSON><PERSON><PERSON><PERSON> on tyhjä", "New password cannot contain blank space.": "Uusi salasana ei voi sisältää välilyöntejä.", "the user's owner and name should not be empty": "käyttäjän omistaja ja nimi eivät saa olla tyhjiä"}, "util": {"No application is found for userId: %s": "Sovellusta ei löydy userId:lle: %s", "No provider for category: %s is found for application: %s": "Provideria kategorialle: %s ei löydy sovellukselle: %s", "The provider: %s is not found": "Provideria: %s ei löydy"}, "verification": {"Invalid captcha provider.": "Virheellinen captcha-provider.", "Phone number is invalid in your region %s": "Puhelinnumero on virheellinen alueellasi %s", "The verification code has already been used!": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on jo k<PERSON><PERSON>!", "The verification code has not been sent yet!": "Vahvistuskoodia ei ole vielä lähetetty!", "Turing test failed.": "Turing-testi ep<PERSON>.", "Unable to get the email modify rule.": "Sähköpostin muokkaussääntöä ei saada.", "Unable to get the phone modify rule.": "<PERSON><PERSON><PERSON><PERSON> muo<PERSON>ntöä ei saada.", "Unknown type": "Tuntematon tyyppi", "Wrong verification code!": "Väärä vahvistuskoodi!", "You should verify your code in %d min!": "Sinun tulee vahvistaa koodisi %d minuutissa!", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "lisää SMS-provider \"Providers\"-listalle sovellukselle: %s", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "lisää sähköposti-provider \"Providers\"-listalle sovellukselle: %s", "the user does not exist, please sign up first": "käyttäjää ei ole o<PERSON>, rekisterö<PERSON> ensin"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "Kutsu ensin WebAuthnSigninBegin"}}