{"account": {"Failed to add user": "ユーザーの追加に失敗しました", "Get init score failed, error: %w": "イニットスコアの取得に失敗しました。エラー：%w", "Please sign out first": "最初にサインアウトしてください", "The application does not allow to sign up new account": "アプリケーションは新しいアカウントの登録を許可しません"}, "auth": {"Challenge method should be S256": "チャレンジメソッドはS256である必要があります", "DeviceCode Invalid": "デバイスコードが無効です", "Failed to create user, user information is invalid: %s": "ユーザーの作成に失敗しました。ユーザー情報が無効です：%s", "Failed to login in: %s": "ログインできませんでした：%s", "Invalid token": "無効なトークン", "State expected: %s, but got: %s": "期待される状態： %s、実際には：%s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "プロバイダーのアカウント：%s とユーザー名：%s（%s）が存在せず、新しいアカウントを %%s 経由でサインアップすることはできません。他の方法でサインアップしてください", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "プロバイダー名：%sとユーザー名：%s（%s）のアカウントは存在しません。新しいアカウントとしてサインアップすることはできません。 ITサポートに連絡してください", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "プロバイダのアカウント：%s とユーザー名：%s (%s) は既に別のアカウント：%s (%s) にリンクされています", "The application: %s does not exist": "アプリケーション: %sは存在しません", "The login method: login with LDAP is not enabled for the application": "このアプリケーションでは LDAP ログインは有効になっていません", "The login method: login with SMS is not enabled for the application": "このアプリケーションでは SMS ログインは有効になっていません", "The login method: login with email is not enabled for the application": "このアプリケーションではメールログインは有効になっていません", "The login method: login with face is not enabled for the application": "このアプリケーションでは顔認証ログインは有効になっていません", "The login method: login with password is not enabled for the application": "ログイン方法：パスワードでのログインはアプリケーションで有効になっていません", "The organization: %s does not exist": "組織「%s」は存在しません", "The provider: %s does not exist": "プロバイダ「%s」は存在しません", "The provider: %s is not enabled for the application": "プロバイダー：%sはアプリケーションでは有効化されていません", "Unauthorized operation": "不正操作", "Unknown authentication type (not password or provider), form = %s": "不明な認証タイプ（パスワードまたはプロバイダーではない）フォーム=%s", "User's tag: %s is not listed in the application's tags": "ユーザータグ「%s」はアプリケーションのタグに含まれていません", "UserCode Expired": "ユーザーコードの有効期限が切れています", "UserCode Invalid": "ユーザーコードが無効です", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "有料ユーザー「%s」には有効または保留中のサブスクリプションがなく、アプリケーション「%s」にはデフォルトの価格設定がありません", "the application for user %s is not found": "ユーザー「%s」のアプリケーションが見つかりません", "the organization: %s is not found": "組織「%s」が見つかりません"}, "cas": {"Service %s and %s do not match": "サービス%sと%sは一致しません"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s は CIDR フォーマットの要件を満たしていません: %s", "Affiliation cannot be blank": "所属は空白にできません", "CIDR for IP: %s should not be empty": "IP「%s」の CIDR は空にできません", "Default code does not match the code's matching rules": "デフォルトコードがコードの一致ルールに一致しません", "DisplayName cannot be blank": "表示名は空白にできません", "DisplayName is not valid real name": "表示名は有効な実名ではありません", "Email already exists": "メールは既に存在します", "Email cannot be empty": "メールが空白にできません", "Email is invalid": "電子メールは無効です", "Empty username.": "空のユーザー名。", "Face data does not exist, cannot log in": "顔認証データが存在しないため、ログインできません", "Face data mismatch": "顔認証データが一致しません", "Failed to parse client IP: %s": "クライアント IP「%s」の解析に失敗しました", "FirstName cannot be blank": "ファーストネームは空白にできません", "Invitation code cannot be blank": "招待コードは空にできません", "Invitation code exhausted": "招待コードの使用回数が上限に達しました", "Invitation code is invalid": "招待コードが無効です", "Invitation code suspended": "招待コードは一時的に無効化されています", "LDAP user name or password incorrect": "Ldapのユーザー名またはパスワードが間違っています", "LastName cannot be blank": "姓は空白にできません", "Multiple accounts with same uid, please check your ldap server": "同じuidを持つ複数のアカウントがあります。あなたのLDAPサーバーを確認してください", "Organization does not exist": "組織は存在しません", "Password cannot be empty": "パスワードは空にできません", "Phone already exists": "電話はすでに存在しています", "Phone cannot be empty": "電話は空っぽにできません", "Phone number is invalid": "電話番号が無効です", "Please register using the email corresponding to the invitation code": "招待コードに対応するメールアドレスで登録してください", "Please register using the phone  corresponding to the invitation code": "招待コードに対応する電話番号で登録してください", "Please register using the username corresponding to the invitation code": "招待コードに対応するユーザー名で登録してください", "Session outdated, please login again": "セッションが期限切れになりました。再度ログインしてください", "The invitation code has already been used": "この招待コードは既に使用されています", "The user is forbidden to sign in, please contact the administrator": "ユーザーはサインインできません。管理者に連絡してください", "The user: %s doesn't exist in LDAP server": "ユーザー「%s」は LDAP サーバーに存在しません", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "ユーザー名には英数字、アンダースコア、ハイフンしか含めることができません。連続したハイフンまたはアンダースコアは不可であり、ハイフンまたはアンダースコアで始まるまたは終わることもできません。", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "アカウントフィールド「%s」の値「%s」がアカウント項目の正規表現に一致しません", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "アプリケーション「%s」のサインアップ項目「%s」の値「%s」が正規表現に一致しません", "Username already exists": "ユーザー名はすでに存在しています", "Username cannot be an email address": "ユーザー名には電子メールアドレスを使用できません", "Username cannot contain white spaces": "ユーザ名にはスペースを含めることはできません", "Username cannot start with a digit": "ユーザー名は数字で始めることはできません", "Username is too long (maximum is 255 characters).": "ユーザー名が長すぎます（最大255文字）。", "Username must have at least 2 characters": "ユーザー名は少なくとも2文字必要です", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "ユーザー名はメール形式もサポートします。ユーザー名は英数字、アンダースコア、またはハイフンのみを含め、連続するハイフンやアンダースコアは使用できません。また、ハイフンまたはアンダースコアで始まったり終わったりすることもできません。メール形式にも注意してください。", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "あなたは間違ったパスワードまたはコードを何度も入力しました。%d 分間待ってから再度お試しください", "Your IP address: %s has been banned according to the configuration of: ": "あなたの IP アドレス「%s」は設定によりアクセスが禁止されています: ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "パスワードの有効期限が切れています。「パスワードを忘れた方はこちら」をクリックしてリセットしてください", "Your region is not allow to signup by phone": "あなたの地域は電話でサインアップすることができません", "password or code is incorrect": "パスワードまたはコードが正しくありません", "password or code is incorrect, you have %s remaining chances": "パスワードまたはコードが間違っています。あと %s 回の試行機会があります", "unsupported password type: %s": "サポートされていないパスワードタイプ：%s"}, "enforcer": {"the adapter: %s is not found": "アダプタ「%s」が見つかりません"}, "general": {"Failed to import groups": "グループのインポートに失敗しました", "Failed to import users": "ユーザーのインポートに失敗しました", "Missing parameter": "不足しているパラメーター", "Only admin user can specify user": "管理者ユーザーのみがユーザーを指定できます", "Please login first": "最初にログインしてください", "The organization: %s should have one application at least": "組織「%s」は少なくとも1つのアプリケーションを持っている必要があります", "The user: %s doesn't exist": "そのユーザー：%sは存在しません", "Wrong userId": "無効なユーザーIDです", "don't support captchaProvider: ": "captchaProviderをサポートしないでください", "this operation is not allowed in demo mode": "この操作はデモモードでは許可されていません", "this operation requires administrator to perform": "この操作は管理者権限が必要です"}, "ldap": {"Ldap server exist": "LDAPサーバーは存在します"}, "link": {"Please link first": "最初にリンクしてください", "This application has no providers": "このアプリケーションにはプロバイダーがありません", "This application has no providers of type": "「このアプリケーションには、タイプのプロバイダーがありません」と翻訳されます", "This provider can't be unlinked": "このプロバイダーはリンク解除できません", "You are not the global admin, you can't unlink other users": "あなたはグローバル管理者ではありません、他のユーザーとのリンクを解除することはできません", "You can't unlink yourself, you are not a member of any application": "あなたは自分自身をアンリンクすることはできません、あなたはどのアプリケーションのメンバーでもありません"}, "organization": {"Only admin can modify the %s.": "管理者のみが%sを変更できます。", "The %s is immutable.": "%sは不変です。", "Unknown modify rule %s.": "未知の変更ルール%s。", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "「built-in」（組み込み）組織への新しいユーザーの追加は現在無効になっています。注意：「built-in」組織のすべてのユーザーは、Casdoor のグローバル管理者です。ドキュメントを参照してください：https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself。「built-in」組織のユーザーを作成したい場合は、組織の設定ページに移動し、「特権同意を持つ」オプションを有効にしてください。"}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "権限「%s」は存在しません"}, "provider": {"Invalid application id": "アプリケーションIDが無効です", "the provider: %s does not exist": "プロバイダー%sは存在しません"}, "resource": {"User is nil for tag: avatar": "ユーザーはタグ「アバター」に対してnilです", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "ユーザー名または完全なファイルパスが空です：ユーザー名 = %s、完全なファイルパス = %s"}, "saml": {"Application %s not found": "アプリケーション%sは見つかりません"}, "saml_sp": {"provider %s's category is not SAML": "プロバイダ %s のカテゴリはSAMLではありません"}, "service": {"Empty parameters for emailForm: %v": "EmailFormの空のパラメーター：％v", "Invalid Email receivers: %s": "無効な電子メール受信者：%s", "Invalid phone receivers: %s": "電話受信者が無効です：%s"}, "storage": {"The objectKey: %s is not allowed": "オブジェクトキー %s は許可されていません", "The provider type: %s is not supported": "プロバイダータイプ：%sはサポートされていません"}, "subscription": {"Error": "エラー"}, "token": {"Grant_type: %s is not supported in this application": "grant_type：%sはこのアプリケーションでサポートされていません", "Invalid application or wrong clientSecret": "無効なアプリケーションまたは誤ったクライアントシークレットです", "Invalid client_id": "client_idが無効です", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "リダイレクトURI：%sは許可されたリダイレクトURIリストに存在しません", "Token not found, invalid accessToken": "トークンが見つかりません。無効なアクセストークンです"}, "user": {"Display name cannot be empty": "表示名は空にできません", "MFA email is enabled but email is empty": "MFA メールが有効になっていますが、メールアドレスが空です", "MFA phone is enabled but phone number is empty": "MFA 電話番号が有効になっていますが、電話番号が空です", "New password cannot contain blank space.": "新しいパスワードにはスペースを含めることはできません。", "the user's owner and name should not be empty": "ユーザーのオーナーと名前は空にできません"}, "util": {"No application is found for userId: %s": "ユーザーIDに対するアプリケーションが見つかりません： %s", "No provider for category: %s is found for application: %s": "アプリケーション：%sのカテゴリ%sのプロバイダが見つかりません", "The provider: %s is not found": "プロバイダー：%sが見つかりません"}, "verification": {"Invalid captcha provider.": "無効なCAPTCHAプロバイダー。", "Phone number is invalid in your region %s": "電話番号はあなたの地域で無効です %s", "The verification code has already been used!": "この検証コードは既に使用されています！", "The verification code has not been sent yet!": "検証コードはまだ送信されていません！", "Turing test failed.": "チューリングテストは失敗しました。", "Unable to get the email modify rule.": "電子メール変更規則を取得できません。", "Unable to get the phone modify rule.": "電話の変更ルールを取得できません。", "Unknown type": "不明なタイプ", "Wrong verification code!": "誤った検証コードです！", "You should verify your code in %d min!": "あなたは%d分であなたのコードを確認する必要があります！", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "アプリケーション「%s」の「Providers」リストに SMS プロバイダを追加してください", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "アプリケーション「%s」の「Providers」リストにメールプロバイダを追加してください", "the user does not exist, please sign up first": "ユーザーは存在しません。まず登録してください"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "最初にWebAuthnSigninBeginを呼び出してください"}}