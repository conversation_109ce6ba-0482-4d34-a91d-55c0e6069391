{"account": {"Failed to add user": "Nepodařilo se přidat uživatele", "Get init score failed, error: %w": "Nepodařilo se získat počáteční skóre, chyba: %w", "Please sign out first": "Nejprve se prosím odhlaste", "The application does not allow to sign up new account": "Aplikace neumožňuje registraci nového účtu"}, "auth": {"Challenge method should be S256": "Metoda výzvy by <PERSON><PERSON><PERSON> b<PERSON>t S256", "DeviceCode Invalid": "DeviceCode je neplat<PERSON>ý", "Failed to create user, user information is invalid: %s": "Nepodařilo se vytvořit uživatele, informace o uživateli jsou neplatné: %s", "Failed to login in: %s": "Nepodařilo se přihlásit: %s", "Invalid token": "Neplatný token", "State expected: %s, but got: %s": "Očekávaný stav: %s, ale získán: %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "Účet pro poskytovatele: %s a uživatelské jméno: %s (%s) neexistuje a není povoleno se registrovat jako nový účet přes %%s, prosím použijte jiný způsob registrace", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "Účet pro poskytovatele: %s a uživatelské jméno: %s (%s) neexistuje a není povoleno se registrovat jako nový <PERSON>č<PERSON>, prosím kontaktujte svou IT podporu", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "Účet pro poskytovatele: %s a uživatelské jméno: %s (%s) je již propojen s jiným účtem: %s (%s)", "The application: %s does not exist": "Aplikace: %s neexistuje", "The login method: login with LDAP is not enabled for the application": "Přihlašovací metoda: přihlášení pomocí LDAP není pro aplikaci povolena", "The login method: login with SMS is not enabled for the application": "Přihlašovací metoda: přihlášení pomocí SMS není pro aplikaci povolena", "The login method: login with email is not enabled for the application": "Přihlašovací metoda: přihlášení pomocí e-mailu není pro aplikaci povolena", "The login method: login with face is not enabled for the application": "Přihlašovací metoda: přihlášení pomocí obličeje není pro aplikaci povolena", "The login method: login with password is not enabled for the application": "Metoda přihlášení: přihlášení pomocí hesla není pro aplikaci povolena", "The organization: %s does not exist": "Organizace: %s neexistuje", "The provider: %s does not exist": "Poskytovatel: %s neexistuje", "The provider: %s is not enabled for the application": "Poskytovatel: %s není pro aplikaci povolen", "Unauthorized operation": "Neoprávněná operace", "Unknown authentication type (not password or provider), form = %s": "Neznámý typ autentizace (nen<PERSON> he<PERSON>lo nebo poskytovatel), formulář = %s", "User's tag: %s is not listed in the application's tags": "Uživatelův tag: %s není uveden v tagech aplikace", "UserCode Expired": "UserCode vypršel", "UserCode Invalid": "UserCode je neplat<PERSON>ý", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "Placený uživatel %s nemá aktivní ani čekající předplatné a aplikace: %s nemá výchozí ceny", "the application for user %s is not found": "Aplikace pro uživatele %s nebyla nalezena", "the organization: %s is not found": "Organizace: %s nebyla na<PERSON>a"}, "cas": {"Service %s and %s do not match": "Služba %s a %s se neshodují"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s nesplňuje požadavky formátu CIDR: %s", "Affiliation cannot be blank": "Příslušnost nemůže být prázdná", "CIDR for IP: %s should not be empty": "CIDR pro IP: %s by <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Default code does not match the code's matching rules": "Výchozí kód neodpovídá pravidlům shody kódu", "DisplayName cannot be blank": "Zobrazované j<PERSON>no nemůže být prázdné", "DisplayName is not valid real name": "Zobrazované j<PERSON>no není platné skuteč<PERSON> j<PERSON>no", "Email already exists": "Email ji<PERSON>", "Email cannot be empty": "<PERSON><PERSON> b<PERSON> prázdný", "Email is invalid": "<PERSON><PERSON> je <PERSON>", "Empty username.": "Prázdné u<PERSON>ivatelské jméno.", "Face data does not exist, cannot log in": "Data obličeje neexistují, nelze se přihlásit", "Face data mismatch": "Neshoda dat obličeje", "Failed to parse client IP: %s": "Nepodařilo se parsovat IP klienta: %s", "FirstName cannot be blank": "Křestní j<PERSON>no nemůže být prázdné", "Invitation code cannot be blank": "Pozvánkový kód nemůže být prázdný", "Invitation code exhausted": "Pozvánkový kód je vyčerpán", "Invitation code is invalid": "Pozvánkový kód je neplat<PERSON>ý", "Invitation code suspended": "Pozvánkový kód je pozastaven", "LDAP user name or password incorrect": "Uživatelské jméno nebo heslo LDAP je nesprávné", "LastName cannot be blank": "Příjmení nemůže být prázdné", "Multiple accounts with same uid, please check your ldap server": "<PERSON><PERSON><PERSON> se stejným uid, prosím zkontrolujte svůj ldap server", "Organization does not exist": "Organizace neexistuje", "Password cannot be empty": "<PERSON><PERSON><PERSON> být prázdné", "Phone already exists": "Telefon již existuje", "Phone cannot be empty": "Telefon nemůže být prázdný", "Phone number is invalid": "Telefonní číslo je neplatné", "Please register using the email corresponding to the invitation code": "Prosím registrujte se pomocí e-mailu odpovídají<PERSON><PERSON><PERSON> p<PERSON> kódu", "Please register using the phone  corresponding to the invitation code": "Prosím registrujte se pomocí telefonu odpovídající<PERSON> p<PERSON> kódu", "Please register using the username corresponding to the invitation code": "Prosím registrujte se pomocí uživatelského jména odpovídajícího p<PERSON> kódu", "Session outdated, please login again": "<PERSON><PERSON> je z<PERSON>ara<PERSON>, prosím přihlaste se znovu", "The invitation code has already been used": "Pozvánkový kód již byl použit", "The user is forbidden to sign in, please contact the administrator": "Uživatel má zakázáno se přihlásit, prosím kontaktujte administrátora", "The user: %s doesn't exist in LDAP server": "Uživatel: %s neexistuje na LDAP serveru", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "Uživatelské jméno může obsahovat pouze alfanume<PERSON>, podtržítka nebo pomlčky, nem<PERSON>že mít po sobě jdoucí pomlčky nebo podtržítka a nemůže začínat nebo končit pomlčkou nebo podtržítkem.", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "Hodnota \\\"%s\\\" pro pole účtu \\\"%s\\\" neodpovídá regexu položky účtu", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "Hodnota \\\"%s\\\" pro pole registrace \\\"%s\\\" neodpovídá regexu položky registrace aplikace \\\"%s\\\"", "Username already exists": "Uživatelské jméno již existuje", "Username cannot be an email address": "Uživatelské j<PERSON>no nemůže být emailová adresa", "Username cannot contain white spaces": "Uživatelské jméno nemůže obsahovat mezery", "Username cannot start with a digit": "Uživatelské jméno nemůže začínat číslicí", "Username is too long (maximum is 255 characters).": "Uživatelsk<PERSON> j<PERSON> je p<PERSON><PERSON> (maximálně 255 znaků).", "Username must have at least 2 characters": "Uživatelské jméno musí mít alespoň 2 znaky", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "Uživatelské jméno podporuje formát e-mailu. <PERSON><PERSON><PERSON> uživate<PERSON>ké jméno může obsahovat pouze alfanumerick<PERSON> znaky, podtržítka nebo pomlčky, nemůže mít souvislé pomlčky nebo podtržítka a nemůže začínat nebo končit pomlčkou nebo podtržítkem. Také dbejte na formát e-mailu.", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "Z<PERSON>li jste špatné he<PERSON>lo nebo kód p<PERSON><PERSON><PERSON>, prosím počkejte %d minut a zkuste to znovu", "Your IP address: %s has been banned according to the configuration of: ": "Vaše IP adresa: %s byla zablokována podle konfigurace: ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "<PERSON>aš<PERSON> heslo v<PERSON>prš<PERSON>. Prosím resetujte si heslo kliknutím na \\\"Zapomněl jsem heslo\\\"", "Your region is not allow to signup by phone": "Vaše oblast neumožňuje registraci pomocí telefonu", "password or code is incorrect": "he<PERSON><PERSON> nebo kód je ne<PERSON>ý", "password or code is incorrect, you have %s remaining chances": "he<PERSON><PERSON> nebo kód je nesp<PERSON>, máte %s zbývajících pokusů", "unsupported password type: %s": "nepodporovaný typ hesla: %s"}, "enforcer": {"the adapter: %s is not found": "adaptér: %s nebyl nalezen"}, "general": {"Failed to import groups": "Nepodařilo se <PERSON>ovat skupiny", "Failed to import users": "Nepodařilo se <PERSON>ovat uživatele", "Missing parameter": "Chybějící parametr", "Only admin user can specify user": "<PERSON>uze administr<PERSON><PERSON> mů<PERSON> určit uživatele", "Please login first": "Prosím, přihlaste se nejprve", "The organization: %s should have one application at least": "Organizace: %s by m<PERSON><PERSON> mít alespoň jednu aplikaci", "The user: %s doesn't exist": "Uživatel: %s neexistuje", "Wrong userId": "Nesprávné uživatelské ID", "don't support captchaProvider: ": "nepod<PERSON><PERSON><PERSON>: ", "this operation is not allowed in demo mode": "tato operace není povolena v demo režimu", "this operation requires administrator to perform": "tato operace vyžaduje administrátora k provedení"}, "ldap": {"Ldap server exist": "Ldap server existuje"}, "link": {"Please link first": "<PERSON><PERSON><PERSON><PERSON>, nej<PERSON><PERSON> propojte", "This application has no providers": "<PERSON>to a<PERSON> nemá žádné poskytovatele", "This application has no providers of type": "Tato ap<PERSON>ace nemá žádné poskytovatele typu", "This provider can't be unlinked": "<PERSON>to posky<PERSON>l nemůže být odpojen", "You are not the global admin, you can't unlink other users": "Nejste globální administrátor, nemůžete odpojovat jin<PERSON>", "You can't unlink yourself, you are not a member of any application": "Nemůžete od<PERSON> sami sebe, nejste členem žádné aplikace"}, "organization": {"Only admin can modify the %s.": "Pouze administrátor může upravit %s.", "The %s is immutable.": "%s je ne<PERSON>.", "Unknown modify rule %s.": "Neznámé pravidlo úpravy %s.", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "Přidání nového uživatele do 'vestavěné' organizace je momentálně zakázáno. Poznámka: všichni uživatelé v 'vestavěné' organizaci jsou globálními správci v Casdooru. Viz docs: https://casdoor.org/docs/basic/core-concepts#how  -dělá-casdoor-spravovat-sám. Pokud stále chcete vytvořit uživatele pro 'vestavěnou' organizaci, přejděte na stránku nastavení organizace a aktivujte možnost '<PERSON><PERSON> so<PERSON> s oprávněními'."}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "Oprávnění: \\\"%s\\\" neexistuje"}, "provider": {"Invalid application id": "Neplatné ID aplikace", "the provider: %s does not exist": "poskytovatel: %s neexistuje"}, "resource": {"User is nil for tag: avatar": "<PERSON><PERSON><PERSON><PERSON> je nil pro tag: avatar", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "Uživatelské jméno nebo úplná cesta k souboru je prázdná: uživatelské jméno = %s, úplná cesta k souboru = %s"}, "saml": {"Application %s not found": "Aplikace %s nebyla nalezena"}, "saml_sp": {"provider %s's category is not SAML": "poskytovatel %s není kategorie SAML"}, "service": {"Empty parameters for emailForm: %v": "Prázdné parametry pro emailForm: %v", "Invalid Email receivers: %s": "Neplatní příjemci emailu: %s", "Invalid phone receivers: %s": "Neplatní příjemci telefonu: %s"}, "storage": {"The objectKey: %s is not allowed": "objectKey: %s nen<PERSON> povolen", "The provider type: %s is not supported": "typ poskytovatele: %s nen<PERSON>n"}, "subscription": {"Error": "Chyba"}, "token": {"Grant_type: %s is not supported in this application": "Grant_type: %s není v této aplikaci podporován", "Invalid application or wrong clientSecret": "Neplatná aplikace nebo špatný clientSecret", "Invalid client_id": "Neplatné client_id", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "Přesměrovací URI: %s neexistuje v seznamu povolených přesměrovacích URI", "Token not found, invalid accessToken": "To<PERSON> ne<PERSON>, neplatný accessToken"}, "user": {"Display name cannot be empty": "Zobrazované j<PERSON>no nemůže být prázdné", "MFA email is enabled but email is empty": "MFA e-mail je povolen, ale e-mail je pr<PERSON><PERSON><PERSON><PERSON>", "MFA phone is enabled but phone number is empty": "MFA telefon je povolen, ale telefonní číslo je prázdné", "New password cannot contain blank space.": "<PERSON>é he<PERSON>lo ne<PERSON>ůže obsahovat prázdné místo.", "the user's owner and name should not be empty": "vlastník a j<PERSON> už<PERSON> by <PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "util": {"No application is found for userId: %s": "Pro userId: %s nebyla nalezena žádná aplikace", "No provider for category: %s is found for application: %s": "Pro kategorii: %s nebyl nalezen žádný poskytovatel pro aplikaci: %s", "The provider: %s is not found": "Poskytovatel: %s nebyl nalezen"}, "verification": {"Invalid captcha provider.": "Neplatný poskytovatel captcha.", "Phone number is invalid in your region %s": "Telefonní číslo je ve vaší oblasti %s neplatné", "The verification code has already been used!": "Ověřovací kód již byl použit!", "The verification code has not been sent yet!": "Ověřovací kód ještě nebyl odeslán!", "Turing test failed.": "Turingův test selhal.", "Unable to get the email modify rule.": "Nelze získat pravidlo pro úpravu emailu.", "Unable to get the phone modify rule.": "Nelze získat pravidlo pro úpravu telefonu.", "Unknown type": "Neznámý typ", "Wrong verification code!": "Špatný ověřovací kód!", "You should verify your code in %d min!": "<PERSON><PERSON><PERSON> byste ověřit svůj kód do %d minut!", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "prosím přidejte SMS poskytovatele do seznamu \\\"Providers\\\" pro aplikaci: %s", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "prosím přidejte e-mailového poskytovatele do seznamu \\\"Providers\\\" pro aplikaci: %s", "the user does not exist, please sign up first": "<PERSON><PERSON><PERSON><PERSON> neexistuje, prosím nejprve se zaregistrujte"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "<PERSON><PERSON><PERSON><PERSON>, nejprve zavolejte WebAuthnSigninBegin"}}