{"account": {"Failed to add user": "添加用户失败", "Get init score failed, error: %w": "初始化分数失败: %w", "Please sign out first": "请先退出登录", "The application does not allow to sign up new account": "该应用不允许注册新用户"}, "auth": {"Challenge method should be S256": "Challenge方法应该为S256", "DeviceCode Invalid": "DeviceCode 无效", "Failed to create user, user information is invalid: %s": "创建用户失败，用户信息无效: %s", "Failed to login in: %s": "登录失败: %s", "Invalid token": "无效token", "State expected: %s, but got: %s": "期望状态为: %s, 实际状态为: %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "提供商账户: %s 与用户名: %s (%s) 不存在且 不允许通过 %s 注册新账户, 请使用其他方式注册", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "提供商账户: %s 与用户名: %s (%s) 不存在且 不允许注册新账户, 请联系IT支持", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "提供商账户: %s与用户名: %s (%s)已经与其他账户绑定: %s (%s)", "The application: %s does not exist": "应用%s不存在", "The login method: login with LDAP is not enabled for the application": "该应用禁止采用LDAP登录方式", "The login method: login with SMS is not enabled for the application": "该应用禁止采用短信登录方式", "The login method: login with email is not enabled for the application": "该应用禁止采用邮箱登录方式", "The login method: login with face is not enabled for the application": "该应用禁止采用人脸登录", "The login method: login with password is not enabled for the application": "该应用禁止采用密码登录方式", "The organization: %s does not exist": "组织: %s 不存在", "The provider: %s does not exist": "提供商: %s 不存在", "The provider: %s is not enabled for the application": "该应用的提供商: %s未被启用", "Unauthorized operation": "未授权的操作", "Unknown authentication type (not password or provider), form = %s": "未知的认证类型（非密码或第三方提供商）：%s", "User's tag: %s is not listed in the application's tags": "用户的标签: %s不在该应用的标签列表中", "UserCode Expired": "用户代码已过期", "UserCode Invalid": "用户代码无效", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "paid-user %s 没有激活或正在等待订阅并且应用: %s 没有默认值", "the application for user %s is not found": "未找到用户 %s 的应用程序", "the organization: %s is not found": "组织: %s 不存在"}, "cas": {"Service %s and %s do not match": "服务%s与%s不匹配"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s 不符合 CIDR 的格式要求: %s", "Affiliation cannot be blank": "工作单位不可为空", "CIDR for IP: %s should not be empty": "IP 的 CIDR 格式：%s 不能为空", "Default code does not match the code's matching rules": "邀请码默认值和邀请码规则不匹配", "DisplayName cannot be blank": "显示名称不可为空", "DisplayName is not valid real name": "显示名称必须是真实姓名", "Email already exists": "该邮箱已存在", "Email cannot be empty": "邮箱不可为空", "Email is invalid": "无效邮箱", "Empty username.": "用户名不可为空", "Face data does not exist, cannot log in": "未录入人脸数据，无法登录", "Face data mismatch": "人脸不匹配", "Failed to parse client IP: %s": "无法解析客户端 IP 地址: %s", "FirstName cannot be blank": "名不可以为空", "Invitation code cannot be blank": "邀请码不能为空", "Invitation code exhausted": "邀请码使用次数已耗尽", "Invitation code is invalid": "邀请码无效", "Invitation code suspended": "邀请码已被禁止使用", "LDAP user name or password incorrect": "LDAP密码错误", "LastName cannot be blank": "姓不可以为空", "Multiple accounts with same uid, please check your ldap server": "多个帐户具有相同的uid，请检查您的 LDAP 服务器", "Organization does not exist": "组织不存在", "Password cannot be empty": "密码不能为空", "Phone already exists": "该手机号已存在", "Phone cannot be empty": "手机号不可为空", "Phone number is invalid": "无效手机号", "Please register using the email corresponding to the invitation code": "请使用邀请码关联的邮箱注册", "Please register using the phone  corresponding to the invitation code": "请使用邀请码关联的手机号注册", "Please register using the username corresponding to the invitation code": "请使用邀请码关联的用户名注册", "Session outdated, please login again": "会话已过期，请重新登录", "The invitation code has already been used": "邀请码已被使用", "The user is forbidden to sign in, please contact the administrator": "该用户被禁止登录，请联系管理员", "The user: %s doesn't exist in LDAP server": "用户: %s 在LDAP服务器中未找到", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "用户名只能包含字母数字字符、下划线或连字符，不能有连续的连字符或下划线，也不能以连字符或下划线开头或结尾", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "值 \\\"%s\\\"在账户信息字段\\\"%s\\\" 中与应用的账户项正则表达式不匹配", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "值\\\"%s\\\"在注册字段\\\"%s\\\"中与应用\\\"%s\\\"的注册项正则表达式不匹配", "Username already exists": "用户名已存在", "Username cannot be an email address": "用户名不可以是邮箱地址", "Username cannot contain white spaces": "用户名禁止包含空格", "Username cannot start with a digit": "用户名禁止使用数字开头", "Username is too long (maximum is 255 characters).": "用户名过长（最大允许长度为255个字符）", "Username must have at least 2 characters": "用户名至少要有2个字符", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "用户名支持电子邮件格式。此外，用户名只能包含字母数字字符、下划线或连字符，不能包含连续的连字符或下划线，也不能以连字符或下划线开头或结尾。同时请注意电子邮件格式。", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "密码错误次数已达上限，请在 %d 分后重试", "Your IP address: %s has been banned according to the configuration of: ": "您的IP地址：%s 根据以下配置已被禁止： ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "您的密码已过期。请点击 \\\"忘记密码\\\" 以重置密码", "Your region is not allow to signup by phone": "所在地区不支持手机号注册", "password or code is incorrect": "密码错误", "password or code is incorrect, you have %s remaining chances": "密码错误，您还有 %s 次尝试的机会", "unsupported password type: %s": "不支持的密码类型: %s"}, "enforcer": {"the adapter: %s is not found": "适配器: %s 未找到"}, "general": {"Failed to import groups": "导入群组失败", "Failed to import users": "导入用户失败", "Missing parameter": "缺少参数", "Only admin user can specify user": "仅管理员用户可以指定用户", "Please login first": "请先登录", "The organization: %s should have one application at least": "组织: %s 应该拥有至少一个应用", "The user: %s doesn't exist": "用户: %s不存在", "Wrong userId": "错误的 userId", "don't support captchaProvider: ": "不支持验证码提供商: ", "this operation is not allowed in demo mode": "demo模式下不允许该操作", "this operation requires administrator to perform": "只有管理员才能进行此操作"}, "ldap": {"Ldap server exist": "LDAP服务器已存在"}, "link": {"Please link first": "请先绑定", "This application has no providers": "该应用无可用的提供商", "This application has no providers of type": "应用没有该类型的提供商", "This provider can't be unlinked": "该提供商被禁止解绑", "You are not the global admin, you can't unlink other users": "您不是全局管理员，无法解绑其他用户", "You can't unlink yourself, you are not a member of any application": "您无法自行解绑，您不是任何应用程序的成员"}, "organization": {"Only admin can modify the %s.": "仅允许管理员可以修改%s", "The %s is immutable.": "%s 是不可变的", "Unknown modify rule %s.": "未知的修改规则: %s", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "目前，向'built-in'组织添加新用户的功能已禁用。请注意：'built-in'组织中的所有用户均为Casdoor的全局管理员。请参阅文档：https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself。如果您仍希望为built-in组织创建用户，请转到该组织的设置页面并启用“特权同意”选项。"}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "权限: \\\"%s\\\" 不存在"}, "provider": {"Invalid application id": "无效的应用ID", "the provider: %s does not exist": "提供商: %s不存在"}, "resource": {"User is nil for tag: avatar": "上传头像时用户为空", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "username或fullFilePath为空: username = %s, fullFilePath = %s"}, "saml": {"Application %s not found": "未找到应用: %s"}, "saml_sp": {"provider %s's category is not SAML": "提供商: %s不是SAML类型"}, "service": {"Empty parameters for emailForm: %v": "邮件参数为空: %v", "Invalid Email receivers: %s": "无效的邮箱收件人: %s", "Invalid phone receivers: %s": "无效的手机短信收信人: %s"}, "storage": {"The objectKey: %s is not allowed": "objectKey: %s被禁止", "The provider type: %s is not supported": "不支持的提供商类型: %s"}, "subscription": {"Error": "错误"}, "token": {"Grant_type: %s is not supported in this application": "该应用不支持Grant_type: %s", "Invalid application or wrong clientSecret": "无效应用或错误的clientSecret", "Invalid client_id": "无效的ClientId", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "重定向 URI：%s在许可跳转列表中未找到", "Token not found, invalid accessToken": "未查询到对应token, accessToken无效"}, "user": {"Display name cannot be empty": "显示名称不可为空", "MFA email is enabled but email is empty": "MFA 电子邮件已启用，但电子邮件为空", "MFA phone is enabled but phone number is empty": "MFA 电话已启用，但电话号码为空", "New password cannot contain blank space.": "新密码不可以包含空格", "the user's owner and name should not be empty": "用户的组织和名称不能为空"}, "util": {"No application is found for userId: %s": "未找到用户: %s的应用", "No provider for category: %s is found for application: %s": "未找到类别为: %s的提供商来满足应用: %s", "The provider: %s is not found": "未找到提供商: %s"}, "verification": {"Invalid captcha provider.": "非法的验证码提供商", "Phone number is invalid in your region %s": "您所在地区的电话号码无效 %s", "The verification code has already been used!": "验证码已使用过!", "The verification code has not been sent yet!": "验证码未发送！", "Turing test failed.": "验证码还未发送", "Unable to get the email modify rule.": "无法获取邮箱修改规则", "Unable to get the phone modify rule.": "无法获取手机号修改规则", "Unknown type": "未知类型", "Wrong verification code!": "验证码错误!", "You should verify your code in %d min!": "请在 %d 分钟内输入正确验证码", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "请添加一个SMS提供商到应用： %s 的 \\\"提供商 \\\"列表", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "请添加一个Email提供商到应用： %s 的 \\\"提供商 \\\"列表", "the user does not exist, please sign up first": "用户不存在，请先注册"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "请先调用WebAuthnSigninBegin函数"}}