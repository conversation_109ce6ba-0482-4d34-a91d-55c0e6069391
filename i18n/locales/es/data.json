{"account": {"Failed to add user": "No se pudo agregar el usuario", "Get init score failed, error: %w": "Error al obtener el puntaje de inicio, error: %w", "Please sign out first": "Por favor, cierra sesión primero", "The application does not allow to sign up new account": "La aplicación no permite registrarse con una cuenta nueva"}, "auth": {"Challenge method should be S256": "El método de desafío debe ser S256", "DeviceCode Invalid": "Código de dispositivo inválido", "Failed to create user, user information is invalid: %s": "No se pudo crear el usuario, la información del usuario es inválida: %s", "Failed to login in: %s": "No se ha podido iniciar sesión en: %s", "Invalid token": "To<PERSON> in<PERSON>lid<PERSON>", "State expected: %s, but got: %s": "Estado esperado: %s, pero se obtuvo: %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "La cuenta para el proveedor: %s y nombre de usuario: %s (%s) no existe y no está permitido registrarse como una cuenta nueva a través de %%s, por favor use otro método para registrarse", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "La cuenta para el proveedor: %s y el nombre de usuario: %s (%s) no existe y no se permite registrarse como una nueva cuenta, por favor contacte a su soporte de TI", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "La cuenta para proveedor: %s y nombre de usuario: %s (%s) ya está vinculada a otra cuenta: %s (%s)", "The application: %s does not exist": "La aplicación: %s no existe", "The login method: login with LDAP is not enabled for the application": "El método de inicio de sesión: inicio de sesión con LDAP no está habilitado para la aplicación", "The login method: login with SMS is not enabled for the application": "El método de inicio de sesión: inicio de sesión con SMS no está habilitado para la aplicación", "The login method: login with email is not enabled for the application": "El método de inicio de sesión: inicio de sesión con correo electrónico no está habilitado para la aplicación", "The login method: login with face is not enabled for the application": "El método de inicio de sesión: inicio de sesión con reconocimiento facial no está habilitado para la aplicación", "The login method: login with password is not enabled for the application": "El método de inicio de sesión: inicio de sesión con contraseña no está habilitado para la aplicación", "The organization: %s does not exist": "La organización: %s no existe", "The provider: %s does not exist": "El proveedor: %s no existe", "The provider: %s is not enabled for the application": "El proveedor: %s no está habilitado para la aplicación", "Unauthorized operation": "Operación no autorizada", "Unknown authentication type (not password or provider), form = %s": "Tipo de autenticación desconocido (no es contraseña o proveedor), formulario = %s", "User's tag: %s is not listed in the application's tags": "La etiqueta del usuario: %s no está incluida en las etiquetas de la aplicación", "UserCode Expired": "Código de usuario expirado", "UserCode Invalid": "Código de usuario inválido", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "El usuario de pago %s no tiene una suscripción activa o pendiente y la aplicación: %s no tiene precio predeterminado", "the application for user %s is not found": "no se encontró la aplicación para el usuario %s", "the organization: %s is not found": "no se encontró la organización: %s"}, "cas": {"Service %s and %s do not match": "Los servicios %s y %s no coinciden"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s no cumple con los requisitos del formato CIDR: %s", "Affiliation cannot be blank": "Afiliación no puede estar en blanco", "CIDR for IP: %s should not be empty": "El CIDR para la IP: %s no debe estar vacío", "Default code does not match the code's matching rules": "El código predeterminado no coincide con las reglas de coincidencia de códigos", "DisplayName cannot be blank": "El nombre de visualización no puede estar en blanco", "DisplayName is not valid real name": "El nombre de pantalla no es un nombre real válido", "Email already exists": "El correo electrónico ya existe", "Email cannot be empty": "El correo electrónico no puede estar vacío", "Email is invalid": "El correo electrónico no es válido", "Empty username.": "Nombre de usuario vacío.", "Face data does not exist, cannot log in": "No existen datos faciales, no se puede iniciar sesión", "Face data mismatch": "Los datos faciales no coinciden", "Failed to parse client IP: %s": "Error al analizar la IP del cliente: %s", "FirstName cannot be blank": "El nombre no puede estar en blanco", "Invitation code cannot be blank": "El código de invitación no puede estar vacío", "Invitation code exhausted": "Código de invitación agotado", "Invitation code is invalid": "Código de invitación inválido", "Invitation code suspended": "Código de invitación suspendido", "LDAP user name or password incorrect": "Nombre de usuario o contraseña de Ldap incorrectos", "LastName cannot be blank": "El apellido no puede estar en blanco", "Multiple accounts with same uid, please check your ldap server": "Cuentas múltiples con el mismo uid, por favor revise su servidor ldap", "Organization does not exist": "La organización no existe", "Password cannot be empty": "La contraseña no puede estar vacía", "Phone already exists": "El teléfono ya existe", "Phone cannot be empty": "Teléfono no puede estar vacío", "Phone number is invalid": "El número de teléfono no es válido", "Please register using the email corresponding to the invitation code": "Regístrese usando el correo electrónico correspondiente al código de invitación", "Please register using the phone  corresponding to the invitation code": "Regístrese usando el número de teléfono correspondiente al código de invitación", "Please register using the username corresponding to the invitation code": "Regístrese usando el nombre de usuario correspondiente al código de invitación", "Session outdated, please login again": "Sesión expirada, por favor vuelva a iniciar sesión", "The invitation code has already been used": "El código de invitación ya ha sido utilizado", "The user is forbidden to sign in, please contact the administrator": "El usuario no está autorizado a iniciar sesión, por favor contacte al administrador", "The user: %s doesn't exist in LDAP server": "El usuario: %s no existe en el servidor LDAP", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "El nombre de usuario solo puede contener caracteres alfanuméricos, guiones bajos o guiones, no puede tener guiones o subrayados consecutivos, y no puede comenzar ni terminar con un guión o subrayado.", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "El valor \\\"%s\\\" para el campo de cuenta \\\"%s\\\" no coincide con la expresión regular del elemento de cuenta", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "El valor \\\"%s\\\" para el campo de registro \\\"%s\\\" no coincide con la expresión regular del elemento de registro de la aplicación \\\"%s\\\"", "Username already exists": "El nombre de usuario ya existe", "Username cannot be an email address": "Nombre de usuario no puede ser una dirección de correo electrónico", "Username cannot contain white spaces": "Nombre de usuario no puede contener espacios en blanco", "Username cannot start with a digit": "El nombre de usuario no puede empezar con un dígito", "Username is too long (maximum is 255 characters).": "El nombre de usuario es demasiado largo (el máximo es de 255 caracteres).", "Username must have at least 2 characters": "Nombre de usuario debe tener al menos 2 caracteres", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "El nombre de usuario admite formato de correo electrónico. Además, el nombre de usuario solo puede contener caracteres alfanuméricos, guiones bajos o guiones, no puede tener guiones bajos o guiones consecutivos y no puede comenzar ni terminar con un guión o guión bajo. También preste atención al formato del correo electrónico.", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "Has ingresado la contraseña o código incorrecto demasiadas veces, por favor espera %d minutos e intenta de nuevo", "Your IP address: %s has been banned according to the configuration of: ": "Su dirección IP: %s ha sido bloqueada según la configuración de: ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "Su contraseña ha expirado. Restablezca su contraseña haciendo clic en \\\"Olvidé mi contraseña\\\"", "Your region is not allow to signup by phone": "Tu región no está permitida para registrarse por teléfono", "password or code is incorrect": "contraseña o código incorrecto", "password or code is incorrect, you have %s remaining chances": "Contraseña o código incorrecto, tienes %s intentos restantes", "unsupported password type: %s": "Tipo de contraseña no compatible: %s"}, "enforcer": {"the adapter: %s is not found": "no se encontró el adaptador: %s"}, "general": {"Failed to import groups": "Error al importar grupos", "Failed to import users": "Error al importar usuarios", "Missing parameter": "Parámetro faltante", "Only admin user can specify user": "Solo el usuario administrador puede especificar usuario", "Please login first": "Por favor, inicia sesión primero", "The organization: %s should have one application at least": "La organización: %s debe tener al menos una aplicación", "The user: %s doesn't exist": "El usuario: %s no existe", "Wrong userId": "ID de usuario <PERSON>o", "don't support captchaProvider: ": "No apoyo a captchaProvider", "this operation is not allowed in demo mode": "esta operación no está permitida en modo de demostración", "this operation requires administrator to perform": "esta operación requiere que el administrador la realice"}, "ldap": {"Ldap server exist": "El servidor LDAP existe"}, "link": {"Please link first": "Por favor, enlaza primero", "This application has no providers": "Esta aplicación no tiene proveedores", "This application has no providers of type": "Esta aplicación no tiene proveedores del tipo", "This provider can't be unlinked": "Este proveedor no se puede desvincular", "You are not the global admin, you can't unlink other users": "No eres el administrador global, no puedes desvincular a otros usuarios", "You can't unlink yourself, you are not a member of any application": "No puedes desvincularte, no eres miembro de ninguna aplicación"}, "organization": {"Only admin can modify the %s.": "Solo el administrador puede modificar los %s.", "The %s is immutable.": "El %s es inmutable.", "Unknown modify rule %s.": "Regla de modificación desconocida %s.", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "La adición de un nuevo usuario a la organización 'integrada' está actualmente deshabilitada. Tenga en cuenta: todos los usuarios de la organización 'integrada' son administradores globales en Casdoor. Consulte los docs: https://casdoor.org/docs/basic/core-concepts#how  -does-casdoor-manage-itself. Si todavía desea crear un usuario para la organización 'integrada', vaya a la página de configuración de la organización y habilite la opción 'Tiene consentimiento de privilegios'."}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "El permiso: \\\"%s\\\" no existe"}, "provider": {"Invalid application id": "Identificación de aplicación no válida", "the provider: %s does not exist": "El proveedor: %s no existe"}, "resource": {"User is nil for tag: avatar": "El usuario es nulo para la etiqueta: avatar", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "Nombre de usuario o ruta completa de archivo está vacío: nombre de usuario = %s, ruta completa de archivo = %s"}, "saml": {"Application %s not found": "Aplicación %s no encontrada"}, "saml_sp": {"provider %s's category is not SAML": "La categoría del proveedor %s no es SAML"}, "service": {"Empty parameters for emailForm: %v": "Parámetros vacíos para el formulario de correo electrónico: %v", "Invalid Email receivers: %s": "Receptores de correo electrónico no válidos: %s", "Invalid phone receivers: %s": "Receptores de teléfono no válidos: %s"}, "storage": {"The objectKey: %s is not allowed": "El objectKey: %s no está permitido", "The provider type: %s is not supported": "El tipo de proveedor: %s no es compatible"}, "subscription": {"Error": "Error"}, "token": {"Grant_type: %s is not supported in this application": "El tipo de subvención: %s no es compatible con esta aplicación", "Invalid application or wrong clientSecret": "Solicitud inválida o clientSecret incorrecto", "Invalid client_id": "Identificador de cliente no válido", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "El URI de redirección: %s no existe en la lista de URI de redirección permitidos", "Token not found, invalid accessToken": "Token no encontrado, accessToken inválido"}, "user": {"Display name cannot be empty": "El nombre de pantalla no puede estar vacío", "MFA email is enabled but email is empty": "El correo electrónico MFA está habilitado pero el correo está vacío", "MFA phone is enabled but phone number is empty": "El teléfono MFA está habilitado pero el número de teléfono está vacío", "New password cannot contain blank space.": "La nueva contraseña no puede contener espacios en blanco.", "the user's owner and name should not be empty": "el propietario y el nombre del usuario no deben estar vacíos"}, "util": {"No application is found for userId: %s": "No se encuentra ninguna aplicación para el Id de usuario: %s", "No provider for category: %s is found for application: %s": "No se encuentra un proveedor para la categoría: %s para la aplicación: %s", "The provider: %s is not found": "El proveedor: %s no se encuentra"}, "verification": {"Invalid captcha provider.": "Proveed<PERSON> de captcha no válido.", "Phone number is invalid in your region %s": "El número de teléfono es inválido en tu región %s", "The verification code has already been used!": "¡El código de verificación ya ha sido utilizado!", "The verification code has not been sent yet!": "¡El código de verificación aún no ha sido enviado!", "Turing test failed.": "El test de Turing falló.", "Unable to get the email modify rule.": "No se puede obtener la regla de modificación de correo electrónico.", "Unable to get the phone modify rule.": "No se pudo obtener la regla de modificación del teléfono.", "Unknown type": "Tipo desconocido", "Wrong verification code!": "¡Código de verificación incorrecto!", "You should verify your code in %d min!": "¡Deberías verificar tu código en %d minutos!", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "agregue un proveedor de SMS a la lista \\\"Proveedores\\\" para la aplicación: %s", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "agregue un proveedor de correo electrónico a la lista \\\"Proveedores\\\" para la aplicación: %s", "the user does not exist, please sign up first": "El usuario no existe, por favor regístrese primero"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "Por favor, llama primero a WebAuthnSigninBegin"}}