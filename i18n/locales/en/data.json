{"account": {"Failed to add user": "Failed to add user", "Get init score failed, error: %w": "Get init score failed, error: %w", "Please sign out first": "Please sign out first", "The application does not allow to sign up new account": "The application does not allow to sign up new account"}, "auth": {"Challenge method should be S256": "Challenge method should be S256", "DeviceCode Invalid": "DeviceCode Invalid", "Failed to create user, user information is invalid: %s": "Failed to create user, user information is invalid: %s", "Failed to login in: %s": "Failed to login in: %s", "Invalid token": "Invalid token", "State expected: %s, but got: %s": "State expected: %s, but got: %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)", "The application: %s does not exist": "The application: %s does not exist", "The login method: login with LDAP is not enabled for the application": "The login method: login with LDAP is not enabled for the application", "The login method: login with SMS is not enabled for the application": "The login method: login with SMS is not enabled for the application", "The login method: login with email is not enabled for the application": "The login method: login with email is not enabled for the application", "The login method: login with face is not enabled for the application": "The login method: login with face is not enabled for the application", "The login method: login with password is not enabled for the application": "The login method: login with password is not enabled for the application", "The organization: %s does not exist": "The organization: %s does not exist", "The provider: %s does not exist": "The provider: %s does not exist", "The provider: %s is not enabled for the application": "The provider: %s is not enabled for the application", "Unauthorized operation": "Unauthorized operation", "Unknown authentication type (not password or provider), form = %s": "Unknown authentication type (not password or provider), form = %s", "User's tag: %s is not listed in the application's tags": "User's tag: %s is not listed in the application's tags", "UserCode Expired": "UserCode Expired", "UserCode Invalid": "UserCode Invalid", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing", "the application for user %s is not found": "the application for user %s is not found", "the organization: %s is not found": "the organization: %s is not found"}, "cas": {"Service %s and %s do not match": "Service %s and %s do not match"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s does not meet the CIDR format requirements: %s", "Affiliation cannot be blank": "Affiliation cannot be blank", "CIDR for IP: %s should not be empty": "CIDR for IP: %s should not be empty", "Default code does not match the code's matching rules": "Default code does not match the code's matching rules", "DisplayName cannot be blank": "DisplayName cannot be blank", "DisplayName is not valid real name": "DisplayName is not valid real name", "Email already exists": "Email already exists", "Email cannot be empty": "Email cannot be empty", "Email is invalid": "<PERSON><PERSON> is invalid", "Empty username.": "Empty username.", "Face data does not exist, cannot log in": "Face data does not exist, cannot log in", "Face data mismatch": "Face data mismatch", "Failed to parse client IP: %s": "Failed to parse client IP: %s", "FirstName cannot be blank": "FirstName cannot be blank", "Invitation code cannot be blank": "Invitation code cannot be blank", "Invitation code exhausted": "Invitation code exhausted", "Invitation code is invalid": "Invitation code is invalid", "Invitation code suspended": "Invitation code suspended", "LDAP user name or password incorrect": "LDAP user name or password incorrect", "LastName cannot be blank": "LastName cannot be blank", "Multiple accounts with same uid, please check your ldap server": "Multiple accounts with same uid, please check your ldap server", "Organization does not exist": "Organization does not exist", "Password cannot be empty": "Password cannot be empty", "Phone already exists": "Phone already exists", "Phone cannot be empty": "Phone cannot be empty", "Phone number is invalid": "Phone number is invalid", "Please register using the email corresponding to the invitation code": "Please register using the email corresponding to the invitation code", "Please register using the phone  corresponding to the invitation code": "Please register using the phone  corresponding to the invitation code", "Please register using the username corresponding to the invitation code": "Please register using the username corresponding to the invitation code", "Session outdated, please login again": "Session outdated, please login again", "The invitation code has already been used": "The invitation code has already been used", "The user is forbidden to sign in, please contact the administrator": "The user is forbidden to sign in, please contact the administrator", "The user: %s doesn't exist in LDAP server": "The user: %s doesn't exist in LDAP server", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"", "Username already exists": "Username already exists", "Username cannot be an email address": "Username cannot be an email address", "Username cannot contain white spaces": "Username cannot contain white spaces", "Username cannot start with a digit": "<PERSON>rna<PERSON> cannot start with a digit", "Username is too long (maximum is 255 characters).": "Username is too long (maximum is 255 characters).", "Username must have at least 2 characters": "<PERSON><PERSON><PERSON> must have at least 2 characters", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "You have entered the wrong password or code too many times, please wait for %d minutes and try again", "Your IP address: %s has been banned according to the configuration of: ": "Your IP address: %s has been banned according to the configuration of: ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"", "Your region is not allow to signup by phone": "Your region is not allow to signup by phone", "password or code is incorrect": "password or code is incorrect", "password or code is incorrect, you have %s remaining chances": "password or code is incorrect, you have %s remaining chances", "unsupported password type: %s": "unsupported password type: %s"}, "enforcer": {"the adapter: %s is not found": "the adapter: %s is not found"}, "general": {"Failed to import groups": "Failed to import groups", "Failed to import users": "Failed to import users", "Missing parameter": "Missing parameter", "Only admin user can specify user": "Only admin user can specify user", "Please login first": "Please login first", "The organization: %s should have one application at least": "The organization: %s should have one application at least", "The user: %s doesn't exist": "The user: %s doesn't exist", "Wrong userId": "Wrong userId", "don't support captchaProvider: ": "don't support <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: ", "this operation is not allowed in demo mode": "this operation is not allowed in demo mode", "this operation requires administrator to perform": "this operation requires administrator to perform"}, "ldap": {"Ldap server exist": "Ldap server exist"}, "link": {"Please link first": "Please link first", "This application has no providers": "This application has no providers", "This application has no providers of type": "This application has no providers of type", "This provider can't be unlinked": "This provider can't be unlinked", "You are not the global admin, you can't unlink other users": "You are not the global admin, you can't unlink other users", "You can't unlink yourself, you are not a member of any application": "You can't unlink yourself, you are not a member of any application"}, "organization": {"Only admin can modify the %s.": "Only admin can modify the %s.", "The %s is immutable.": "The %s is immutable.", "Unknown modify rule %s.": "Unknown modify rule %s.", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option."}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "The permission: \\\"%s\\\" doesn't exist"}, "provider": {"Invalid application id": "Invalid application id", "the provider: %s does not exist": "the provider: %s does not exist"}, "resource": {"User is nil for tag: avatar": "User is nil for tag: avatar", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "Username or fullFilePath is empty: username = %s, fullFilePath = %s"}, "saml": {"Application %s not found": "Application %s not found"}, "saml_sp": {"provider %s's category is not SAML": "provider %s's category is not SAML"}, "service": {"Empty parameters for emailForm: %v": "Empty parameters for emailForm: %v", "Invalid Email receivers: %s": "Invalid Email receivers: %s", "Invalid phone receivers: %s": "Invalid phone receivers: %s"}, "storage": {"The objectKey: %s is not allowed": "The objectKey: %s is not allowed", "The provider type: %s is not supported": "The provider type: %s is not supported"}, "subscription": {"Error": "Error"}, "token": {"Grant_type: %s is not supported in this application": "Grant_type: %s is not supported in this application", "Invalid application or wrong clientSecret": "Invalid application or wrong clientSecret", "Invalid client_id": "Invalid client_id", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "Redirect URI: %s doesn't exist in the allowed Redirect URI list", "Token not found, invalid accessToken": "Token not found, invalid accessToken"}, "user": {"Display name cannot be empty": "Display name cannot be empty", "MFA email is enabled but email is empty": "MFA email is enabled but email is empty", "MFA phone is enabled but phone number is empty": "MFA phone is enabled but phone number is empty", "New password cannot contain blank space.": "New password cannot contain blank space.", "the user's owner and name should not be empty": "the user's owner and name should not be empty"}, "util": {"No application is found for userId: %s": "No application is found for userId: %s", "No provider for category: %s is found for application: %s": "No provider for category: %s is found for application: %s", "The provider: %s is not found": "The provider: %s is not found"}, "verification": {"Invalid captcha provider.": "Invalid captcha provider.", "Phone number is invalid in your region %s": "Phone number is invalid in your region %s", "The verification code has already been used!": "The verification code has already been used!", "The verification code has not been sent yet!": "The verification code has not been sent yet!", "Turing test failed.": "Turing test failed.", "Unable to get the email modify rule.": "Unable to get the email modify rule.", "Unable to get the phone modify rule.": "Unable to get the phone modify rule.", "Unknown type": "Unknown type", "Wrong verification code!": "Wrong verification code!", "You should verify your code in %d min!": "You should verify your code in %d min!", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "please add a SMS provider to the \\\"Providers\\\" list for the application: %s", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "please add an Email provider to the \\\"Providers\\\" list for the application: %s", "the user does not exist, please sign up first": "the user does not exist, please sign up first"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "Please call WebAuthnSigninBegin first"}}