{"account": {"Failed to add user": "عدم موفقیت در افزودن کاربر", "Get init score failed, error: %w": "عدم موفقیت در دریافت امتیاز اولیه، خطا: %w", "Please sign out first": "لطفاً ابتدا خارج شوید", "The application does not allow to sign up new account": "برنامه اجازه ثبت‌نام حساب جدید را نمی‌دهد"}, "auth": {"Challenge method should be S256": "روش چالش باید S256 باشد", "DeviceCode Invalid": "کد دستگاه نامعتبر است", "Failed to create user, user information is invalid: %s": "عدم موفقیت در ایجاد کاربر، اطلاعات کاربر نامعتبر است: %s", "Failed to login in: %s": "عدم موفقیت در ورود: %s", "Invalid token": "توکن نامعتبر", "State expected: %s, but got: %s": "وضعیت مورد انتظار: %s، اما دریافت شد: %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "حساب برای ارائه‌دهنده: %s و نام کاربری: %s (%s) وجود ندارد و مجاز به ثبت‌نام به‌عنوان حساب جدید از طریق %%s نیست، لطفاً از روش دیگری برای ثبت‌نام استفاده کنید", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "حساب برای ارائه‌دهنده: %s و نام کاربری: %s (%s) وجود ندارد و مجاز به ثبت‌نام به‌عنوان حساب جدید نیست، لطفاً با پشتیبانی IT خود تماس بگیرید", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "حساب برای ارائه‌دهنده: %s و نام کاربری: %s (%s) در حال حاضر به حساب دیگری مرتبط است: %s (%s)", "The application: %s does not exist": "برنامه: %s وجود ندارد", "The login method: login with LDAP is not enabled for the application": "روش ورود: ورود با LDAP برای برنامه فعال نیست", "The login method: login with SMS is not enabled for the application": "روش ورود: ورود با پیامک برای برنامه فعال نیست", "The login method: login with email is not enabled for the application": "روش ورود: ورود با ایمیل برای برنامه فعال نیست", "The login method: login with face is not enabled for the application": "روش ورود: ورود با چهره برای برنامه فعال نیست", "The login method: login with password is not enabled for the application": "روش ورود: ورود با رمز عبور برای برنامه فعال نیست", "The organization: %s does not exist": "سازمان: %s وجود ندارد", "The provider: %s does not exist": "ارائه‌کننده: %s وجود ندارد", "The provider: %s is not enabled for the application": "ارائه‌دهنده: %s برای برنامه فعال نیست", "Unauthorized operation": "عملیات غیرمجاز", "Unknown authentication type (not password or provider), form = %s": "نوع احراز هویت ناشناخته (نه رمز عبور و نه ارائه‌دهنده)، فرم = %s", "User's tag: %s is not listed in the application's tags": "برچسب کاربر: %s در برچسب‌های برنامه فهرست نشده است", "UserCode Expired": "کد کاربر منقضی شده است", "UserCode Invalid": "کد کاربر نامعتبر است", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "کاربر پرداختی %s اشتراک فعال یا در انتظار ندارد و برنامه: %s قیمت‌گذاری پیش‌فرض ندارد", "the application for user %s is not found": " برنامه برای کاربر %s پیدا نشد", "the organization: %s is not found": "سازمان: %s پیدا نشد"}, "cas": {"Service %s and %s do not match": "سرویس %s و %s مطابقت ندارند"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s با نیازهای فرمت CIDR مطابقت ندارد: %s", "Affiliation cannot be blank": "وابستگی نمی‌تواند خالی باشد", "CIDR for IP: %s should not be empty": "CIDR برای IP: %s نباید خالی باشد", "Default code does not match the code's matching rules": "کد پیش‌فرض با قوانین تطبیق کد مطابقت ندارد", "DisplayName cannot be blank": "نام نمایشی نمی‌تواند خالی باشد", "DisplayName is not valid real name": "نام نمایشی یک نام واقعی معتبر نیست", "Email already exists": "ایمیل قبلاً وجود دارد", "Email cannot be empty": "ایمیل نمی‌تواند خالی باشد", "Email is invalid": "ایمیل نامعتبر است", "Empty username.": "نام کاربری خالی است.", "Face data does not exist, cannot log in": "داده‌های چهره وجود ندارد، نمی‌توان وارد شد", "Face data mismatch": "عدم تطابق داده‌های چهره", "Failed to parse client IP: %s": "پارس کردن IP مشتری ناموفق بود: %s", "FirstName cannot be blank": "نام نمی‌تو<PERSON>د خالی باشد", "Invitation code cannot be blank": "کد دعوت نمی‌تواند خالی باشد", "Invitation code exhausted": "کد دعوت استفاده شده است", "Invitation code is invalid": "کد دعوت نامعتبر است", "Invitation code suspended": "کد دعوت معلق است", "LDAP user name or password incorrect": "نام کاربری یا رمز عبور LDAP نادرست است", "LastName cannot be blank": "نام خانوادگی نمی‌تواند خالی باشد", "Multiple accounts with same uid, please check your ldap server": "چندین حساب با uid یکسان، لطفاً سرور LDAP خود را بررسی کنید", "Organization does not exist": "سازمان وجود ندارد", "Password cannot be empty": "رمز عبور نمی‌تواند خالی باشد", "Phone already exists": "تلفن قبلاً وجود دارد", "Phone cannot be empty": "تلفن نمی‌تواند خالی باشد", "Phone number is invalid": "شماره تلفن نامعتبر است", "Please register using the email corresponding to the invitation code": "لطفاً با استفاده از ایمیل مربوط به کد دعوت ثبت‌نام کنید", "Please register using the phone  corresponding to the invitation code": "لطفاً با استفاده از تلفن مربوط به کد دعوت ثبت‌نام کنید", "Please register using the username corresponding to the invitation code": "لطفاً با استفاده از نام کاربری مربوط به کد دعوت ثبت‌نام کنید", "Session outdated, please login again": "جلسه منقضی شده است، لطفاً دوباره وارد شوید", "The invitation code has already been used": "کد دعوت قبلاً استفاده شده است", "The user is forbidden to sign in, please contact the administrator": "ورود کاربر ممنوع است، لطفاً با مدیر تماس بگیرید", "The user: %s doesn't exist in LDAP server": "کاربر: %s در سرور LDAP وجود ندارد", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "نام کاربری فقط می‌تواند حاوی کاراکترهای الفبایی عددی، زیرخط یا خط تیره باشد، نمی‌تواند خط تیره یا زیرخط متوالی داشته باشد، و نمی‌تواند با خط تیره یا زیرخط شروع یا پایان یابد.", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "مقدار \\\"%s\\\" برای فیلد حساب \\\"%s\\\" با regex مورد نظر مطابقت ندارد", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "مقدار \\\"%s\\\" برای فیلد ثبت‌نام \\\"%s\\\" با regex مورد نظر برنامه \\\"%s\\\" مطابقت ندارد", "Username already exists": "نام کاربری قبلاً وجود دارد", "Username cannot be an email address": "نام کاربری نمی‌تواند یک آدرس ایمیل باشد", "Username cannot contain white spaces": "نام کاربری نمی‌تواند حاوی فاصله باشد", "Username cannot start with a digit": "نام کاربری نمی‌تواند با یک رقم شروع شود", "Username is too long (maximum is 255 characters).": "نام کاربری بیش از حد طولانی است (حداکثر ۳۹ کاراکتر).", "Username must have at least 2 characters": "نام کاربری باید حداقل ۲ کاراکتر داشته باشد", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "نام کاربری از فرمت ایمیل پشتیبانی می‌کند. همچنین نام کاربری تنها می‌تواند شامل کاراکترهای الفبایی-عددی، زیرخط یا خط تیره باشد، نمی‌تواند دارای خطوط تیره یا زیرخطوط متوالی باشد و نمی‌تواند با خط تیره یا زیرخط شروع یا پایان یابد. همچنین به فرمت ایمیل توجه کنید.", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "شما رمز عبور یا کد اشتباه را بیش از حد وارد کرده‌اید، لطفاً %d دقیقه صبر کنید و دوباره تلاش کنید", "Your IP address: %s has been banned according to the configuration of: ": "آدرس IP شما: %s طبق تنظیمات بلاک شده است: ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "رمز عبور شما منقضی شده است. لطفاً با کلیک بر روی \\\"فراموشی رمز عبور\\\" رمز عبور خود را بازنشانی کنید", "Your region is not allow to signup by phone": "منطقه شما اجازه ثبت‌نام با تلفن را ندارد", "password or code is incorrect": "رمز عبور یا کد نادرست است", "password or code is incorrect, you have %s remaining chances": "رمز عبور یا کد نادرست است، شما %s فرصت باقی‌مانده دارید", "unsupported password type: %s": "نوع رمز عبور پشتیبانی نشده: %s"}, "enforcer": {"the adapter: %s is not found": "آداپتر: %s پیدا نشد"}, "general": {"Failed to import groups": "ورود گروه‌ها ناموفق بود", "Failed to import users": "عدم موفقیت در وارد کردن کاربران", "Missing parameter": "پارامتر گمشده", "Only admin user can specify user": "فقط کاربر مدیر می‌تواند کاربر را مشخص کند", "Please login first": "لطفاً ابتدا وارد شوید", "The organization: %s should have one application at least": "سازمان: %s باید حداقل یک برنامه داشته باشد", "The user: %s doesn't exist": "کاربر: %s وجود ندارد", "Wrong userId": "شناسه کاربر اشتباه است", "don't support captchaProvider: ": "از captchaProvider پشتیبانی نمی‌شود: ", "this operation is not allowed in demo mode": "این عملیات در حالت دمو مجاز نیست", "this operation requires administrator to perform": "این عملیات نیاز به مدیر برای انجام دارد"}, "ldap": {"Ldap server exist": "سرور LDAP وجود دارد"}, "link": {"Please link first": "لطفاً ابتدا پیوند دهید", "This application has no providers": "این برنامه ارائه‌دهنده‌ای ندارد", "This application has no providers of type": "این برنامه ارائه‌دهنده‌ای از نوع ندارد", "This provider can't be unlinked": "این ارائه‌دهنده نمی‌تواند لغو پیوند شود", "You are not the global admin, you can't unlink other users": "شما مدیر جهانی نیستید، نمی‌توانید کاربران دیگر را لغو پیوند کنید", "You can't unlink yourself, you are not a member of any application": "شما نمی‌توانید خودتان را لغو پیوند کنید، شما عضو هیچ برنامه‌ای نیستید"}, "organization": {"Only admin can modify the %s.": "فقط مدیر می‌تواند %s را تغییر دهد.", "The %s is immutable.": "%s غیرقابل تغییر است.", "Unknown modify rule %s.": "قانون تغییر ناشناخته %s.", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "افزودن کاربر جدید به سازمان «built-in» (درونی) در حال حاضر غیرفعال است. توجه داشته باشید: همه کاربران در سازمان «built-in» مدیران جهانی در Casdoor هستند. به مستندات مراجعه کنید: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. اگر همچنان می‌خواهید یک کاربر برای سازمان «built-in» ایجاد کنید، به صفحه تنظیمات سازمان بروید و گزینه «مجوز موافقت با امتیازات» را فعال کنید."}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "مجوز: \\\"%s\\\" وجود ندارد"}, "provider": {"Invalid application id": "شناسه برنامه نامعتبر", "the provider: %s does not exist": "ارائه‌دهنده: %s وجود ندارد"}, "resource": {"User is nil for tag: avatar": "کاربر برای برچسب: آواتار تهی است", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "نام کاربری یا مسیر کامل فایل خالی است: نام کاربری = %s، مسیر کامل فایل = %s"}, "saml": {"Application %s not found": "برنامه %s یافت نشد"}, "saml_sp": {"provider %s's category is not SAML": "دسته‌بندی ارائه‌دهنده %s SAML نیست"}, "service": {"Empty parameters for emailForm: %v": "پارامترهای خالی برای emailForm: %v", "Invalid Email receivers: %s": "گیرندگان ایمیل نامعتبر: %s", "Invalid phone receivers: %s": "گیرندگان تلفن نامعتبر: %s"}, "storage": {"The objectKey: %s is not allowed": "objectKey: %s مجاز نیست", "The provider type: %s is not supported": "نوع ارائه‌دهنده: %s پشتیبانی نمی‌شود"}, "subscription": {"Error": "خطا"}, "token": {"Grant_type: %s is not supported in this application": "grant_type: %s در این برنامه پشتیبانی نمی‌شود", "Invalid application or wrong clientSecret": "برنامه نامعتبر یا clientSecret نادرست", "Invalid client_id": "client_id نامعتبر", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "آدرس بازگشت: %s در لیست آدرس‌های بازگشت مجاز وجود ندارد", "Token not found, invalid accessToken": "توکن یافت نشد، accessToken نامعتبر"}, "user": {"Display name cannot be empty": "نام نمایشی نمی‌تواند خالی باشد", "MFA email is enabled but email is empty": "ایمیل MFA فعال است اما ایمیل خالی است", "MFA phone is enabled but phone number is empty": "تلفن MFA فعال است اما شماره تلفن خالی است", "New password cannot contain blank space.": "رمز عبور جدید نمی‌تواند حاوی فاصله خالی باشد.", "the user's owner and name should not be empty": "مالک و نام کاربر نباید خالی باشند"}, "util": {"No application is found for userId: %s": "هیچ برنامه‌ای برای userId: %s یافت نشد", "No provider for category: %s is found for application: %s": "هیچ ارائه‌دهنده‌ای برای دسته‌بندی: %s برای برنامه: %s یافت نشد", "The provider: %s is not found": "ارائه‌دهنده: %s یافت نشد"}, "verification": {"Invalid captcha provider.": "ارائه‌دهنده کپچا نامعتبر.", "Phone number is invalid in your region %s": "شماره تلفن در منطقه شما نامعتبر است %s", "The verification code has already been used!": "کد تایید قبلاً استفاده شده است!", "The verification code has not been sent yet!": "کد تأیید هنوز ارسال نشده است!", "Turing test failed.": "تست تورینگ ناموفق بود.", "Unable to get the email modify rule.": "عدم توانایی در دریافت قانون تغییر ایمیل.", "Unable to get the phone modify rule.": "عدم توانایی در دریافت قانون تغییر تلفن.", "Unknown type": "نوع ناشناخته", "Wrong verification code!": "کد تأیید اشتباه!", "You should verify your code in %d min!": "شما باید کد خود را در %d دقیقه تأیید کنید!", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "لطفاً یک ارائه‌کننده SMS به لیست \\\"ارائه‌کنندگان\\\" برای برنامه اضافه کنید: %s", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "لطفاً یک ارائه‌کننده ایمیل به لیست \\\"ارائه‌کنندگان\\\" برای برنامه اضافه کنید: %s", "the user does not exist, please sign up first": "کاربر وجود ندارد، لطفاً ابتدا ثبت‌نام کنید"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "لطفاً ابتدا WebAuthnSigninBegin را فراخوانی کنید"}}