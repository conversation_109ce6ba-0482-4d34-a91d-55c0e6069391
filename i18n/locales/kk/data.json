{"account": {"Failed to add user": "Gebruiker toe<PERSON>n mislukt", "Get init score failed, error: %w": "Initiële score op<PERSON><PERSON> mislukt, fout: %w", "Please sign out first": "<PERSON><PERSON><PERSON> e<PERSON>t uit te loggen", "The application does not allow to sign up new account": "De applicatie staat geen nieuwe registraties toe"}, "auth": {"Challenge method should be S256": "Challenge-methode moet S256 zijn", "DeviceCode Invalid": "DeviceCode ongeldig", "Failed to create user, user information is invalid: %s": "Gebruiker aanmaken mislukt, gebruikersinformatie ongeldig: %s", "Failed to login in: %s": "Inloggen mislukt: %s", "Invalid token": "Ongeldige token", "State expected: %s, but got: %s": "Verwachte state: %s, maar kreeg: %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "Account voor provider: %s en gebruikersnaam: %s (%s) bestaat niet en mag niet registreren via %%s, gebruik een andere methode", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "Account voor provider: %s en gebruikersnaam: %s (%s) bestaat niet en mag niet registreren, contacteer IT-support", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "Account voor provider: %s en gebruikersnaam: %s (%s) is al gelinkt aan ander account: %s (%s)", "The application: %s does not exist": "Applicatie: %s bestaat niet", "The login method: login with LDAP is not enabled for the application": "Inloggen via LDAP is niet ingeschakeld voor deze applicatie", "The login method: login with SMS is not enabled for the application": "Inloggen via SMS is niet ingeschakeld voor deze applicatie", "The login method: login with email is not enabled for the application": "Inloggen via e-mail is niet ingeschakeld voor deze applicatie", "The login method: login with face is not enabled for the application": "Inloggen via gezichtsherkenning is niet ingeschakeld voor deze applicatie", "The login method: login with password is not enabled for the application": "Inloggen via wachtwoord is niet ingeschakeld voor deze applicatie", "The organization: %s does not exist": "Organisatie: %s bestaat niet", "The provider: %s does not exist": "Provider: %s bestaat niet", "The provider: %s is not enabled for the application": "Provider: %s is niet ingeschakeld voor de applicatie", "Unauthorized operation": "Ongeautoriseer<PERSON> handeling", "Unknown authentication type (not password or provider), form = %s": "Onbekend authenticatietype (geen wachtwoord of provider), formulier = %s", "User's tag: %s is not listed in the application's tags": "Gebruikerstag: %s staat niet in de applicatietags", "UserCode Expired": "UserCode verlopen", "UserCode Invalid": "UserCode ongeldig", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "Betaalde gebruiker %s heeft geen actief of lopend abonnement en applicatie: %s heeft geen standaardprijzen", "the application for user %s is not found": "Applicatie voor gebruiker %s niet gevonden", "the organization: %s is not found": "Organisatie: %s niet gevonden"}, "cas": {"Service %s and %s do not match": "Service %s en %s komen niet overeen"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s voldoet niet aan CIDR-formaat: %s", "Affiliation cannot be blank": "Organisatie mag niet leeg zijn", "CIDR for IP: %s should not be empty": "CIDR voor IP: %s mag niet leeg zijn", "Default code does not match the code's matching rules": "Standaardcode komt niet overeen met matching rules", "DisplayName cannot be blank": "Weergavenaam mag niet leeg zijn", "DisplayName is not valid real name": "Weergavenaam is geen geldige echte naam", "Email already exists": "E-mailadres bestaat al", "Email cannot be empty": "E-mailadres mag niet leeg zijn", "Email is invalid": "Ongeldig e-mailadres", "Empty username.": "<PERSON><PERSON> g<PERSON>.", "Face data does not exist, cannot log in": "Gezichtsgegevens ontbreken, inloggen niet mogelijk", "Face data mismatch": "Gezichtsgegevens komen niet overeen", "Failed to parse client IP: %s": "Client-IP parsen mislukt: %s", "FirstName cannot be blank": "<PERSON><PERSON>naam mag niet leeg zijn", "Invitation code cannot be blank": "Uitnodigingscode mag niet leeg zijn", "Invitation code exhausted": "Uitnodigingscode uitgeput", "Invitation code is invalid": "Uitnodigingscode ongeldig", "Invitation code suspended": "Uitnodigingscode opgeschort", "LDAP user name or password incorrect": "LDAP-g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of wachtwoord onjuist", "LastName cannot be blank": "Achternaam mag niet leeg zijn", "Multiple accounts with same uid, please check your ldap server": "Meerdere accounts met <PERSON><PERSON><PERSON> <PERSON><PERSON>, controleer LDAP-server", "Organization does not exist": "Organisatie bestaat niet", "Password cannot be empty": "Wachtwoord mag niet leeg zijn", "Phone already exists": "Telefoonnummer bestaat al", "Phone cannot be empty": "Telefoonnummer mag niet leeg zijn", "Phone number is invalid": "Ongeldig telefoonnummer", "Please register using the email corresponding to the invitation code": "<PERSON><PERSON><PERSON> met het e-mailad<PERSON> dat hoort bij de uitnodigingscode", "Please register using the phone  corresponding to the invitation code": "<PERSON><PERSON><PERSON> met het telefoonnummer dat hoort bij de uitnodigingscode", "Please register using the username corresponding to the invitation code": "<PERSON><PERSON><PERSON> met de gebruikers<PERSON>am die hoort bij de uitnodigingscode", "Session outdated, please login again": "<PERSON><PERSON> verlope<PERSON>, gelieve opnieuw in te loggen", "The invitation code has already been used": "Uitnodigingscode is al gebruikt", "The user is forbidden to sign in, please contact the administrator": "Geb<PERSON><PERSON>r mag niet inloggen, contacteer beheerder", "The user: %s doesn't exist in LDAP server": "Gebruiker: %s bestaat niet in LDAP-server", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "Gebruikersnaam mag alleen alfanumerieke tekens, underscores of koppeltekens bevatten, geen opeenvolgende koppeltekens/underscores, en mag niet beginnen of eindigen met koppelteken of underscore.", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "Хадгаламалық өрісі \\\"%s\\\" үшін мәні \\\"%s\\\" хадгаламалық элементінің регекспімен сәйкес келмейді", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "Тіркелу өрісі \\\"%s\\\" үшін мәні \\\"%s\\\" қолданба \\\"%s\\\"-нің тіркелу элементінің регекспімен сәйкес келмейді", "Username already exists": "Gebruikers<PERSON><PERSON> bestaat al", "Username cannot be an email address": "Gebruikersnaam mag geen e-mailadres zijn", "Username cannot contain white spaces": "Gebruikersnaam mag geen spaties bevatten", "Username cannot start with a digit": "Gebruikersnaam mag niet met cij<PERSON> beginnen", "Username is too long (maximum is 255 characters).": "Gebruikersnaam te lang (maximaal 255 tekens).", "Username must have at least 2 characters": "Gebruikersnaam moet minstens 2 tekens hebben", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "Gebruikersnaam ondersteunt e-mailformaat. Mag alleen alfanumerieke tekens, underscores of koppeltekens bevatten, geen opeenvolgende koppeltekens/underscores, en mag niet beginnen of eindigen met koppelteken of underscore. Let op e-mailformaat.", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "Te vaak verkeerd wachtwoord of code ingevoerd, wacht %d minuten en probeer opnieuw", "Your IP address: %s has been banned according to the configuration of: ": "Je IP-adres: %s is verbannen volgens configuratie van: ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "Сіздің пароліңіз мерзімі аяқталды. Кликтап \\\"Парольді ұмытып қалдым\\\" нұсқасын қалпына келтіріңіз", "Your region is not allow to signup by phone": "Registratie per telefoon niet toe<PERSON> in jouw regio", "password or code is incorrect": "wachtwoord of code onjuist", "password or code is incorrect, you have %s remaining chances": "wachtwoord of code onjuist, nog %s pogingen over", "unsupported password type: %s": "niet-ondersteund wachtwoordtype: %s"}, "enforcer": {"the adapter: %s is not found": "adapter: %s niet gevonden"}, "general": {"Failed to import groups": "Importeren groepen mislukt", "Failed to import users": "Importeren gebruikers mislukt", "Missing parameter": "Ontbrekende parameter", "Only admin user can specify user": "<PERSON><PERSON> beheerder mag gebruiker <PERSON>eren", "Please login first": "<PERSON><PERSON><PERSON> in te loggen", "The organization: %s should have one application at least": "Organisatie: %s moet minstens één applicatie hebben", "The user: %s doesn't exist": "Gebruiker: %s bestaat niet", "Wrong userId": "Verkeerde userId", "don't support captchaProvider: ": "captchaProvider niet ondersteund: ", "this operation is not allowed in demo mode": "deze handeling is niet toe<PERSON><PERSON><PERSON> in demo-modus", "this operation requires administrator to perform": "deze handeling vereist beheerder"}, "ldap": {"Ldap server exist": "LDAP-server bestaat al"}, "link": {"Please link first": "<PERSON><PERSON><PERSON> e<PERSON>t te linken", "This application has no providers": "Deze applicatie heeft geen providers", "This application has no providers of type": "Deze applicatie heeft geen providers van type", "This provider can't be unlinked": "Deze provider kan niet ontkoppeld worden", "You are not the global admin, you can't unlink other users": "Je bent geen globale beheerder, je kunt andere gebruikers niet ont<PERSON>en", "You can't unlink yourself, you are not a member of any application": "Je kunt jezelf niet on<PERSON>, je bent geen lid van enige applicatie"}, "organization": {"Only admin can modify the %s.": "<PERSON>een be<PERSON>r kan %s wijzigen.", "The %s is immutable.": "%s is onveranderlijk.", "Unknown modify rule %s.": "Onbekende wijzigingsregel %s.", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "'Built-in' (құрылымдық) ұйымға жаңа пайдаланушы қосу әзірге өшірілген. Ескеріңіз: 'Built-in' ұйымдағы барлық пайдаланушылар Casdoor дөгел әкімдері болып табылады. Документацияға қараңыз: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. Егер сіз әлі де 'Built-in' ұйымы үшін пайдаланушы жасауын қаласаңыз, ұйымның баптаулар бетinə оралыңыз және 'Рұқсатларға растау бар' опциясын қосу іске ашыңыз."}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "Рұқсат: \\\"%s\\\" жоқ"}, "provider": {"Invalid application id": "Ongeldige applicatie-ID", "the provider: %s does not exist": "provider: %s bestaat niet"}, "resource": {"User is nil for tag: avatar": "G<PERSON><PERSON>ike<PERSON> is nil voor tag: avatar", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "Gebruikersnaam of fullFilePath is leeg: username = %s, fullFilePath = %s"}, "saml": {"Application %s not found": "Applicatie %s niet gevonden"}, "saml_sp": {"provider %s's category is not SAML": "provider %s is niet van categorie SAML"}, "service": {"Empty parameters for emailForm: %v": "Lege parameters voor emailForm: %v", "Invalid Email receivers: %s": "Ongeldige e-mailontvangers: %s", "Invalid phone receivers: %s": "Ongeldige telefoonontvangers: %s"}, "storage": {"The objectKey: %s is not allowed": "objectKey: %s is niet toe<PERSON>an", "The provider type: %s is not supported": "Providertype: %s wordt niet ondersteund"}, "subscription": {"Error": "Fout"}, "token": {"Grant_type: %s is not supported in this application": "Grant_type: %s wordt niet ondersteund in deze applicatie", "Invalid application or wrong clientSecret": "Ongeldige applicatie of verkeerde clientSecret", "Invalid client_id": "Ongeldige client_id", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "Redirect URI: %s staat niet in toegestane lijst", "Token not found, invalid accessToken": "Token niet gevonden, ongeldige accessToken"}, "user": {"Display name cannot be empty": "Weergavenaam mag niet leeg zijn", "MFA email is enabled but email is empty": "MFA-e-mail is ingeschakeld maar e-mailadres is leeg", "MFA phone is enabled but phone number is empty": "MFA-telefoon is ingeschakeld maar telefoonnummer is leeg", "New password cannot contain blank space.": "<PERSON><PERSON>w wachtwoord mag geen spaties bevatten.", "the user's owner and name should not be empty": "eigenaar en naam van gebruiker mogen niet leeg zijn"}, "util": {"No application is found for userId: %s": "Geen applicatie gevonden voor userId: %s", "No provider for category: %s is found for application: %s": "Geen provider voor categorie: %s gevonden voor applicatie: %s", "The provider: %s is not found": "Provider: %s niet gevonden"}, "verification": {"Invalid captcha provider.": "Ongeldige captcha-provider.", "Phone number is invalid in your region %s": "Telefoonnummer ongeldig in regio %s", "The verification code has already been used!": "Verificatiecode is al gebruikt!", "The verification code has not been sent yet!": "Verificatiecode is nog niet verstuurd!", "Turing test failed.": "Turing-test mislukt.", "Unable to get the email modify rule.": "Kan e-mail-wijzigingsregel niet ophalen.", "Unable to get the phone modify rule.": "Kan telefoon-wijzigingsregel niet ophalen.", "Unknown type": "Onbekend type", "Wrong verification code!": "Verkeerde verificatiecode!", "You should verify your code in %d min!": "Verifieer je code binnen %d min!", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "Күріспе: %s қолданбасының \\\"Провайдерлер\\\" тізіміне SMS провайдерін қосыңыз", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "Күріспе: %s қолданбасының \\\"Провайдерлер\\\" тізіміне Электрондық пошта провайдерін қосыңыз", "the user does not exist, please sign up first": "g<PERSON><PERSON><PERSON><PERSON> bestaat niet, registreer e<PERSON>t"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "<PERSON><PERSON> e<PERSON>t WebAuthnSigninBegin aan"}}