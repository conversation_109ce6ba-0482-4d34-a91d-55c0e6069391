{"account": {"Failed to add user": "فشل إضافة المستخدم", "Get init score failed, error: %w": "فشل الحصول على النتيجة الأولية، الخطأ: %w", "Please sign out first": "يرجى تسجيل الخروج أولاً", "The application does not allow to sign up new account": "التطبيق لا يسمح بالتسجيل بحساب جديد"}, "auth": {"Challenge method should be S256": "يجب أن تكون طريقة التحدي S256", "DeviceCode Invalid": "رمز الجهاز غير صالح", "Failed to create user, user information is invalid: %s": "فشل إنشاء المستخدم، معلومات المستخدم غير صالحة: %s", "Failed to login in: %s": "فشل تسجيل الدخول: %s", "Invalid token": "الرمز غير صالح", "State expected: %s, but got: %s": "كان من المتوقع الحالة: %s، لكن حصلنا على: %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "الحساب الخاص بالمزود: %s واسم المستخدم: %s (%s) غير موجود ولا يُسمح بالتسجيل كحساب جديد عبر %%s، يرجى استخدام طريقة أخرى للتسجيل", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "الحساب الخاص بالمزود: %s واسم المستخدم: %s (%s) غير موجود ولا يُسمح بالتسجيل كحساب جديد، يرجى الاتصال بدعم تكنولوجيا المعلومات", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "الحساب الخاص بالمزود: %s واسم المستخدم: %s (%s) مرتبط بالفعل بحساب آخر: %s (%s)", "The application: %s does not exist": "التطبيق: %s غير موجود", "The login method: login with LDAP is not enabled for the application": "طريقة تسجيل الدخول: تسجيل الدخول باستخدام LDAP غير مفعّلة لهذا التطبيق", "The login method: login with SMS is not enabled for the application": "طريقة تسجيل الدخول: تسجيل الدخول باستخدام الرسائل النصية غير مفعّلة لهذا التطبيق", "The login method: login with email is not enabled for the application": "طريقة تسجيل الدخول: تسجيل الدخول باستخدام البريد الإلكتروني غير مفعّلة لهذا التطبيق", "The login method: login with face is not enabled for the application": "طريقة تسجيل الدخول: تسجيل الدخول باستخدام الوجه غير مفعّلة لهذا التطبيق", "The login method: login with password is not enabled for the application": "طريقة تسجيل الدخول: تسجيل الدخول باستخدام كلمة المرور غير مفعّلة لهذا التطبيق", "The organization: %s does not exist": "المنظمة: %s غير موجودة", "The provider: %s does not exist": "المزود: %s غير موجود", "The provider: %s is not enabled for the application": "المزود: %s غير مفعّل لهذا التطبيق", "Unauthorized operation": "عملية غير مصرح بها", "Unknown authentication type (not password or provider), form = %s": "نوع مصادقة غير معروف (ليس كلمة مرور أو مزود)، النموذج = %s", "User's tag: %s is not listed in the application's tags": "وسم المستخدم: %s غير مدرج في وسوم التطبيق", "UserCode Expired": "رمز المستخدم منتهي الصلاحية", "UserCode Invalid": "رمز المستخدم غير صالح", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "المستخدم المدفوع %s ليس لديه اشتراك نشط أو معلق والتطبيق: %s ليس لديه تسعير افتراضي", "the application for user %s is not found": "لم يتم العثور على التطبيق الخاص بالمستخدم %s", "the organization: %s is not found": "لم يتم العثور على المنظمة: %s"}, "cas": {"Service %s and %s do not match": "الخدمة %s و %s غير متطابقتين"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s لا تلبي متطلبات تنسيق CIDR: %s", "Affiliation cannot be blank": "الانتماء لا يمكن أن يكون فارغاً", "CIDR for IP: %s should not be empty": "CIDR لعنوان IP: %s لا يجب أن يكون فارغاً", "Default code does not match the code's matching rules": "الرمز الافتراضي لا يتطابق مع قواعد المطابقة", "DisplayName cannot be blank": "اسم العرض لا يمكن أن يكون فارغاً", "DisplayName is not valid real name": "اسم العرض ليس اسمًا حقيقيًا صالحًا", "Email already exists": "البريد الإلكتروني موجود بالفعل", "Email cannot be empty": "البريد الإلكتروني لا يمكن أن يكون فارغاً", "Email is invalid": "البريد الإلكتروني غير صالح", "Empty username.": "اسم المستخدم فارغ.", "Face data does not exist, cannot log in": "بيانات الوجه غير موجودة، لا يمكن تسجيل الدخول", "Face data mismatch": "عدم تطابق بيانات الوجه", "Failed to parse client IP: %s": "فشل تحليل IP العميل: %s", "FirstName cannot be blank": "الاسم الأول لا يمكن أن يكون فارغاً", "Invitation code cannot be blank": "رمز الدعوة لا يمكن أن يكون فارغاً", "Invitation code exhausted": "رمز الدعوة استُنفِد", "Invitation code is invalid": "رمز الدعوة غير صالح", "Invitation code suspended": "رمز الدعوة موقوف", "LDAP user name or password incorrect": "اسم مستخدم LDAP أو كلمة المرور غير صحيحة", "LastName cannot be blank": "الاسم الأخير لا يمكن أن يكون فارغاً", "Multiple accounts with same uid, please check your ldap server": "حسابات متعددة بنفس uid، يرجى التحقق من خادم ldap الخاص بك", "Organization does not exist": "المنظمة غير موجودة", "Password cannot be empty": "كلمة المرور لا يمكن أن تكون فارغة", "Phone already exists": "الهاتف موجود بالفعل", "Phone cannot be empty": "الهاتف لا يمكن أن يكون فارغاً", "Phone number is invalid": "رقم الهاتف غير صالح", "Please register using the email corresponding to the invitation code": "يرجى التسجيل باستخدام البريد الإلكتروني المطابق لرمز الدعوة", "Please register using the phone  corresponding to the invitation code": "يرجى التسجيل باستخدام الهاتف المطابق لرمز الدعوة", "Please register using the username corresponding to the invitation code": "يرجى التسجيل باستخدام اسم المستخدم المطابق لرمز الدعوة", "Session outdated, please login again": "الجلسة منتهية الصلاحية، يرجى تسجيل الدخول مرة أخرى", "The invitation code has already been used": "رمز الدعوة تم استخدامه بالفعل", "The user is forbidden to sign in, please contact the administrator": "المستخدم ممنوع من تسجيل الدخول، يرجى الاتصال بالمسؤول", "The user: %s doesn't exist in LDAP server": "المستخدم: %s غير موجود في خادم LDAP", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "اسم المستخدم يمكن أن يحتوي فقط على أحرف وأرقام، شرطات سفلية أو علوية، لا يمكن أن تحتوي على شرطات متتالية، ولا يمكن أن يبدأ أو ينتهي بشرطة.", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "Hesap alanı \\\"%s\\\" için \\\"%s\\\" de<PERSON><PERSON>, hesap ö<PERSON>esi regex'iyle <PERSON>", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "<PERSON><PERSON><PERSON> alanı \\\"%s\\\" için \\\"%s\\\" değ<PERSON>, \\\"%s\\\" uygulamasının kayıt öğesi regex'iyle <PERSON>", "Username already exists": "اسم المستخدم موجود بالفعل", "Username cannot be an email address": "اسم المستخدم لا يمكن أن يكون عنوان بريد إلكتروني", "Username cannot contain white spaces": "اسم المستخدم لا يمكن أن يحتوي على مسافات", "Username cannot start with a digit": "اسم المستخدم لا يمكن أن يبدأ برقم", "Username is too long (maximum is 255 characters).": "اسم المستخدم طويل جداً (الحد الأقصى 255 حرفاً).", "Username must have at least 2 characters": "اسم المستخدم يجب أن يحتوي على حرفين على الأقل", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "اسم المستخدم يدعم تنسيق البريد الإلكتروني. كما أن اسم المستخدم يمكن أن يحتوي فقط على أحرف وأرقام، شرطات سفلية أو علوية، لا يمكن أن تحتوي على شرطات متتالية، ولا يمكن أن يبدأ أو ينتهي بشرطة. انتبه أيضًا لتنسيق البريد الإلكتروني.", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "لقد قمت بإدخال كلمة المرور أو الرمز الخطأ عدة مرات، يرجى الانتظار %d دقائق ثم المحاولة مرة أخرى", "Your IP address: %s has been banned according to the configuration of: ": "عنوان IP الخاص بك: %s تم حظره وفقًا لتكوين: ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "Şifrenizin süresi doldu. Lütfen \\\"Şifremi unuttum\\\"a tıklayarak şifrenizi sıfırlayın", "Your region is not allow to signup by phone": "منطقتك لا تسمح بالتسجيل عبر الهاتف", "password or code is incorrect": "كلمة المرور أو الرمز غير صحيح", "password or code is incorrect, you have %s remaining chances": "كلمة المرور أو الرمز غير صحيح، لديك %s فرصة متبقية", "unsupported password type: %s": "نوع كلمة المرور غير مدعوم: %s"}, "enforcer": {"the adapter: %s is not found": "المحول: %s غير موجود"}, "general": {"Failed to import groups": "فشل استيراد المجموعات", "Failed to import users": "فشل استيراد المستخدمين", "Missing parameter": "المعلمة مفقودة", "Only admin user can specify user": "فقط المسؤول يمكنه تحديد المستخدم", "Please login first": "يرجى تسجيل الدخول أولاً", "The organization: %s should have one application at least": "المنظمة: %s يجب أن تحتوي على تطبيق واحد على الأقل", "The user: %s doesn't exist": "المستخدم: %s غير موجود", "Wrong userId": "معرف المستخدم غير صحيح", "don't support captchaProvider: ": "لا يدعم captchaProvider: ", "this operation is not allowed in demo mode": "هذه العملية غير مسموح بها في وضع العرض التوضيحي", "this operation requires administrator to perform": "هذه العملية تتطلب مسؤولاً لتنفيذها"}, "ldap": {"Ldap server exist": "خادم LDAP موجود"}, "link": {"Please link first": "يرجى الربط أولاً", "This application has no providers": "هذا التطبيق لا يحتوي على مزودين", "This application has no providers of type": "هذا التطبيق لا يحتوي على مزودين من النوع", "This provider can't be unlinked": "لا يمكن فصل هذا المزود", "You are not the global admin, you can't unlink other users": "أنت لست المسؤول العام، لا يمكنك فصل مستخدمين آخرين", "You can't unlink yourself, you are not a member of any application": "لا يمكنك فصل نفسك، أنت لست عضواً في أي تطبيق"}, "organization": {"Only admin can modify the %s.": "فقط المسؤول يمكنه تعديل %s.", "The %s is immutable.": "%s غير قابل للتعديل.", "Unknown modify rule %s.": "قاعدة تعديل غير معروفة %s.", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "إضافة مستخدم جديد إلى المنظمة \"المدمجة\" غير متوفر حاليًا. يرجى ملاحظة: جميع المستخدمين في المنظمة \"المدمجة\" هم مسؤولون عالميون في Casdoor. يرجى الرجوع إلى الوثائق: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. إذا كنت لا تزال ترغب في إنشاء مستخدم للمنظمة \"المدمجة\"، اไป إلى صفحة إعدادات المنظمة وقم بتمكين خيار \"لديه موافقة صلاحية\"."}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "İzin: \\\"%s\\\" mevcut değil"}, "provider": {"Invalid application id": "معرف التطبيق غير صالح", "the provider: %s does not exist": "المزود: %s غير موجود"}, "resource": {"User is nil for tag: avatar": "المستخدم nil للوسم: avatar", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "اسم المستخدم أو fullFilePath فارغ: username = %s، fullFilePath = %s"}, "saml": {"Application %s not found": "التطبيق %s غير موجود"}, "saml_sp": {"provider %s's category is not SAML": "فئة المزود %s ليست SAML"}, "service": {"Empty parameters for emailForm: %v": "معلمات فارغة لـ emailForm: %v", "Invalid Email receivers: %s": "مستقبلو البريد الإلكتروني غير صالحين: %s", "Invalid phone receivers: %s": "مستقلو الهاتف غير صالحين: %s"}, "storage": {"The objectKey: %s is not allowed": "مفتاح الكائن: %s غير مسموح به", "The provider type: %s is not supported": "نوع المزود: %s غير مدعوم"}, "subscription": {"Error": "خطأ"}, "token": {"Grant_type: %s is not supported in this application": "Grant_type: %s غير مدعوم في هذا التطبيق", "Invalid application or wrong clientSecret": "تطبيق غير صالح أو clientSecret خاطئ", "Invalid client_id": "client_id غير صالح", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "Redirect URI: %s غير موجود في قائمة Redirect URI المسموح بها", "Token not found, invalid accessToken": "الرمز غير موجود، accessToken غير صالح"}, "user": {"Display name cannot be empty": "اسم العرض لا يمكن أن يكون فارغاً", "MFA email is enabled but email is empty": "تم تمكين MFA للبريد الإلكتروني لكن البريد الإلكتروني فارغ", "MFA phone is enabled but phone number is empty": "تم تمكين MFA للهاتف لكن رقم الهاتف فارغ", "New password cannot contain blank space.": "كلمة المرور الجديدة لا يمكن أن تحتوي على مسافات.", "the user's owner and name should not be empty": "مالك المستخدم واسمه لا يجب أن يكونا فارغين"}, "util": {"No application is found for userId: %s": "لم يتم العثور على تطبيق لـ userId: %s", "No provider for category: %s is found for application: %s": "لم يتم العثور على مزود للفئة: %s للتطبيق: %s", "The provider: %s is not found": "المزود: %s غير موجود"}, "verification": {"Invalid captcha provider.": "مزود captcha غير صالح.", "Phone number is invalid in your region %s": "رقم الهاتف غير صالح في منطقتك %s", "The verification code has already been used!": "رمز التحقق تم استخدامه بالفعل!", "The verification code has not been sent yet!": "رمز التحقق لم يُرسل بعد!", "Turing test failed.": "فشل اختبار تورينغ.", "Unable to get the email modify rule.": "غير قادر على الحصول على قاعدة تعديل البريد الإلكتروني.", "Unable to get the phone modify rule.": "<PERSON>ير قادر على الحصول على قاعدة تعديل الهاتف.", "Unknown type": "نوع غير معروف", "Wrong verification code!": "رمز التحقق خاطئ!", "You should verify your code in %d min!": "يجب عليك التحقق من الرمز خلال %d دقيقة!", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "lütfen uygulama için \\\"Sağlayıcılar\\\" listesine bir SMS sağlayıcı ekleyin: %s", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "lütfen uygulama için \\\"Sağlayıcılar\\\" listesine bir E-posta sağlayıcı ekleyin: %s", "the user does not exist, please sign up first": "المستخدم غير موجود، يرجى التسجيل أولاً"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "يرجى استدعاء WebAuthnSigninBegin أولاً"}}