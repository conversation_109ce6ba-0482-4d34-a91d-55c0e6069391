{"account": {"Failed to add user": "<PERSON><PERSON><PERSON><PERSON> thể thêm người dùng", "Get init score failed, error: %w": "<PERSON><PERSON><PERSON> điểm khởi đầu thất bại, lỗi: %w", "Please sign out first": "<PERSON>ui lòng đăng xuất trước", "The application does not allow to sign up new account": "Ứng dụng không cho phép đăng ký tài khoản mới"}, "auth": {"Challenge method should be S256": "<PERSON>ư<PERSON><PERSON> ph<PERSON>p thách thức nên là S256", "DeviceCode Invalid": "<PERSON><PERSON> thi<PERSON><PERSON> bị không hợp lệ", "Failed to create user, user information is invalid: %s": "<PERSON>hông thể tạo người dùng, thông tin người dùng không hợp lệ: %s", "Failed to login in: %s": "<PERSON><PERSON><PERSON> nhập không thành công: %s", "Invalid token": "<PERSON><PERSON> thông báo không hợp lệ", "State expected: %s, but got: %s": "Trạng thái dự kiến: %s, nhưng nhận được: %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "Tài khoản cho nhà cung cấp: %s và tên người dùng: %s (%s) không tồn tại và không được phép đăng ký làm tài khoản mới qua %%s, vui lòng sử dụng cách khác để đăng ký", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "Tài khoản cho nhà cung cấp: %s và tên người dùng: %s (%s) không tồn tại và không được phép đăng ký như một tài kho<PERSON>n mới, vui lòng liên hệ với bộ phận hỗ trợ công nghệ thông tin của bạn", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "Tài khoản cho nhà cung cấp: %s và tên người dùng: %s (%s) đã được liên kết với tài khoản khác: %s (%s)", "The application: %s does not exist": "Ứng dụng: %s không tồn tại", "The login method: login with LDAP is not enabled for the application": "<PERSON><PERSON><PERSON><PERSON> thức đăng nhập bằng LDAP ch<PERSON><PERSON> đư<PERSON><PERSON> bật cho ứng dụng", "The login method: login with SMS is not enabled for the application": "<PERSON><PERSON><PERSON><PERSON> thức đăng nhập bằng SMS chưa đư<PERSON><PERSON> bật cho ứng dụng", "The login method: login with email is not enabled for the application": "<PERSON><PERSON><PERSON><PERSON> thức đăng nhập bằng email chư<PERSON> đư<PERSON><PERSON> bật cho ứng dụng", "The login method: login with face is not enabled for the application": "<PERSON><PERSON><PERSON><PERSON> thức đăng nhập bằng khuôn mặt chưa được bật cho ứng dụng", "The login method: login with password is not enabled for the application": "<PERSON><PERSON><PERSON><PERSON> thức đăng nhập: đăng nhập bằng mật khẩu không được kích hoạt cho ứng dụng", "The organization: %s does not exist": "Tổ chức: %s không tồn tại", "The provider: %s does not exist": "Nhà cung cấp: %s không tồn tại", "The provider: %s is not enabled for the application": "Nhà cung cấp: %s không đ<PERSON><PERSON><PERSON> kích ho<PERSON>t cho ứng dụng", "Unauthorized operation": "<PERSON><PERSON><PERSON> động không đư<PERSON><PERSON> <PERSON><PERSON> quyền", "Unknown authentication type (not password or provider), form = %s": "<PERSON><PERSON><PERSON> xác thực không xác định (không phải mật khẩu hoặc nhà cung cấp), biểu mẫu = %s", "User's tag: %s is not listed in the application's tags": "Thẻ của người dùng: %s không nằm trong danh sách thẻ của ứng dụng", "UserCode Expired": "<PERSON>ã người dùng đã hết hạn", "UserCode Invalid": "<PERSON><PERSON> người dùng không hợp lệ", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "người dùng trả phí %s không có đăng ký đang hoạt động hoặc đang chờ và ứng dụng: %s không có giá mặc định", "the application for user %s is not found": "kh<PERSON>ng tìm thấy ứng dụng cho người dùng %s", "the organization: %s is not found": "kh<PERSON>ng tìm thấy tổ chức: %s"}, "cas": {"Service %s and %s do not match": "<PERSON><PERSON><PERSON> sang tiếng Việt: <PERSON><PERSON><PERSON> vụ %s và %s không khớp"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s không đáp <PERSON>ng yêu cầu định dạng CIDR: %s", "Affiliation cannot be blank": "Tình trạng liên kết không thể để trống", "CIDR for IP: %s should not be empty": "CIDR cho IP: %s không đư<PERSON>c để trống", "Default code does not match the code's matching rules": "Mã mặc định không khớp với quy tắc khớp mã", "DisplayName cannot be blank": "Tên hiển thị không thể để trống", "DisplayName is not valid real name": "DisplayName không phải là tên thật hợp lệ", "Email already exists": "<PERSON><PERSON> đã tồn tại", "Email cannot be empty": "<PERSON><PERSON> không thể để trống", "Email is invalid": "Địa chỉ email kh<PERSON>ng hợp lệ", "Empty username.": "<PERSON><PERSON><PERSON> đ<PERSON> nh<PERSON>p trống.", "Face data does not exist, cannot log in": "<PERSON><PERSON> liệu khuôn mặt không tồn tại, không thể đăng nhập", "Face data mismatch": "<PERSON><PERSON> liệu khuôn mặt không khớp", "Failed to parse client IP: %s": "Kh<PERSON>ng thể phân tích IP khách: %s", "FirstName cannot be blank": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "Invitation code cannot be blank": "<PERSON><PERSON> mời không đư<PERSON>c để trống", "Invitation code exhausted": "<PERSON>ã mời đã hết", "Invitation code is invalid": "<PERSON><PERSON> mời không hợp lệ", "Invitation code suspended": "<PERSON>ã mời đã bị tạm ngưng", "LDAP user name or password incorrect": "<PERSON>ên người dùng hoặc mật khẩu Ldap không ch<PERSON>h xác", "LastName cannot be blank": "<PERSON><PERSON> không thể để trống", "Multiple accounts with same uid, please check your ldap server": "<PERSON><PERSON><PERSON><PERSON> tài kho<PERSON>n với cùng một uid, vui lòng kiểm tra máy chủ ldap của bạn", "Organization does not exist": "<PERSON><PERSON> chức không tồn tại", "Password cannot be empty": "<PERSON><PERSON><PERSON> kh<PERSON>u không đư<PERSON><PERSON> để trống", "Phone already exists": "<PERSON><PERSON><PERSON><PERSON> thoại đã tồn tại", "Phone cannot be empty": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON><PERSON> không thể để trống", "Phone number is invalid": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ", "Please register using the email corresponding to the invitation code": "Vui lòng đăng ký bằng email tương ứng với mã mời", "Please register using the phone  corresponding to the invitation code": "<PERSON>ui lòng đăng ký bằng số điện thoại tương ứng với mã mời", "Please register using the username corresponding to the invitation code": "<PERSON>ui lòng đăng ký bằng tên người dùng tương ứng với mã mời", "Session outdated, please login again": "<PERSON><PERSON><PERSON> làm vi<PERSON><PERSON> hế<PERSON> hạn, vui lòng đăng nhập lại", "The invitation code has already been used": "<PERSON>ã mời đã đư<PERSON>c sử dụng", "The user is forbidden to sign in, please contact the administrator": "<PERSON><PERSON><PERSON><PERSON> dùng bị cấm đăng <PERSON>, vui lòng liên hệ với quản trị viên", "The user: %s doesn't exist in LDAP server": "Người dùng: %s không tồn tại trên máy chủ LDAP", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "Tên người dùng chỉ có thể chứa các ký tự chữ và số, gạch dưới hoặc gạch ngang, không được có hai ký tự gạch dưới hoặc gạch ngang liền kề và không được bắt đầu hoặc kết thúc bằng dấu gạch dưới hoặc gạch ngang.", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "Giá trị \\\"%s\\\" cho trường tài kho<PERSON>n \\\"%s\\\" không khớp với biểu thức chính quy của mục tài khoản", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "Giá trị \\\"%s\\\" cho trường đăng ký \\\"%s\\\" không khớp với biểu thức chính quy của mục đăng ký trong ứng dụng \\\"%s\\\"", "Username already exists": "Tên đăng nhập đã tồn tại", "Username cannot be an email address": "Tên người dùng không thể là địa chỉ email", "Username cannot contain white spaces": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i dùng không thể chứa khoảng trắng", "Username cannot start with a digit": "<PERSON>ên người dùng không thể bắt đầu bằng chữ số", "Username is too long (maximum is 255 characters).": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> nhập quá dài (tối đa là 255 ký tự).", "Username must have at least 2 characters": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> nhập ph<PERSON>i có ít nhất 2 ký tự", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "Tên người dùng hỗ trợ định dạng email. <PERSON><PERSON><PERSON><PERSON> ra, tên người dùng chỉ có thể chứa ký tự chữ và số, gạch dưới hoặc gạch ngang, không được có gạch ngang hoặc gạch dưới liên tiếp và không được bắt đầu hoặc kết thúc bằng gạch ngang hoặc gạch dưới. Đồng thời lưu ý định dạng email.", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "Bạn đã nhập sai mật khẩu hoặc mã quá nhiều lần, vui lòng đợi %d phút và thử lại", "Your IP address: %s has been banned according to the configuration of: ": "Địa chỉ IP của bạn: %s đã bị cấm theo cấu hình của: ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "Mật khẩu của bạn đã hết hạn. <PERSON><PERSON> lòng đặt lại mật khẩu bằng cách nhấp vào \\\"Quên mật khẩu\\\"", "Your region is not allow to signup by phone": "<PERSON><PERSON>ng của bạn không được phép đăng ký bằng điện thoại", "password or code is incorrect": "mật khẩu hoặc mã không đúng", "password or code is incorrect, you have %s remaining chances": "<PERSON><PERSON><PERSON> khẩu hoặc mã không ch<PERSON>h <PERSON>c, bạn còn %s lần c<PERSON> hội", "unsupported password type: %s": "<PERSON><PERSON><PERSON> mật khẩu không được hỗ trợ: %s"}, "enforcer": {"the adapter: %s is not found": "không tìm thấy adapter: %s"}, "general": {"Failed to import groups": "<PERSON><PERSON><PERSON><PERSON> thể nhập nhóm", "Failed to import users": "<PERSON><PERSON><PERSON><PERSON> thể nhập người dùng", "Missing parameter": "<PERSON><PERSON><PERSON><PERSON> tham số", "Only admin user can specify user": "Chỉ người dùng quản trị mới có thể chỉ định người dùng", "Please login first": "<PERSON><PERSON> lòng đăng nhập trước", "The organization: %s should have one application at least": "Tổ chức: %s cần có ít nhất một ứng dụng", "The user: %s doesn't exist": "Người dùng: %s không tồn tại", "Wrong userId": "ID người dùng sai", "don't support captchaProvider: ": "không hỗ trợ captchaProvider: ", "this operation is not allowed in demo mode": "thao tác này không đ<PERSON><PERSON><PERSON> phép trong chế độ demo", "this operation requires administrator to perform": "thao tác này yêu cầu quản trị viên thực hiện"}, "ldap": {"Ldap server exist": "M<PERSON>y chủ LDAP tồn tại"}, "link": {"Please link first": "<PERSON><PERSON> lòng kết nối tr<PERSON><PERSON><PERSON> tiên", "This application has no providers": "Ứng dụng này không có nhà cung cấp", "This application has no providers of type": "Ứng dụng này không có nhà cung cấp loại nào", "This provider can't be unlinked": "<PERSON><PERSON><PERSON> cung cấp này không thể đư<PERSON><PERSON> tách ra", "You are not the global admin, you can't unlink other users": "Bạn không phải là quản trị viên toàn cầu, bạn không thể hủy liên kết người dùng khác", "You can't unlink yourself, you are not a member of any application": "Bạn không thể hủy liên kết của mình, bởi vì bạn không phải là thành viên của bất kỳ ứng dụng nào"}, "organization": {"Only admin can modify the %s.": "Chỉ những người quản trị mới có thể sửa đổi %s.", "The %s is immutable.": "%s không thể thay đổi được.", "Unknown modify rule %s.": "<PERSON><PERSON> tắc thay đổi không xác định %s.", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "Thêm người dùng mới vào tổ chức 'built-in' (tích hợp sẵn) hiện đang bị vô hiệu hóa. Lưu ý: Tất cả người dùng trong tổ chức 'built-in' đều là quản trị viên toàn cầu trong Casdoor. Xem tài liệu: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. Nếu bạn vẫn muốn tạo người dùng cho tổ chức 'built-in', hãy truy cập trang cài đặt của tổ chức và bật tùy chọn 'Có đồng ý quyền đặc biệt'."}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "Quyền: \\\"%s\\\" không tồn tại"}, "provider": {"Invalid application id": "Sai ID ứng dụng", "the provider: %s does not exist": "Nhà cung cấp: %s không tồn tại"}, "resource": {"User is nil for tag: avatar": "Người dùng không có giá trị cho thẻ: hình đại diện", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "Tên người dùng hoặc đường dẫn tệp đầy đủ trống: tên người dùng = %s, đường dẫn tệp đầy đủ = %s"}, "saml": {"Application %s not found": "Ứng dụng %s không tìm thấy"}, "saml_sp": {"provider %s's category is not SAML": "<PERSON><PERSON> mục của nhà cung cấp %s không phải là SAML"}, "service": {"Empty parameters for emailForm: %v": "Tham số trống cho emailForm: %v", "Invalid Email receivers: %s": "<PERSON>ười nhận <PERSON><PERSON> không hợp lệ: %s", "Invalid phone receivers: %s": "<PERSON><PERSON><PERSON><PERSON> nhận điện thoại không hợp lệ: %s"}, "storage": {"The objectKey: %s is not allowed": "Kh<PERSON><PERSON> đối tượng: %s không đư<PERSON><PERSON> phép", "The provider type: %s is not supported": "Loại nhà cung cấp: %s không được hỗ trợ"}, "subscription": {"Error": "Lỗi"}, "token": {"Grant_type: %s is not supported in this application": "Loạ<PERSON> cấp phép: %s không được hỗ trợ trong ứng dụng này", "Invalid application or wrong clientSecret": "Đơn đăng ký không hợp lệ hoặc sai clientSecret", "Invalid client_id": "Client_id không hợp lệ", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "Đường dẫn chuyển hướng URI: %s không tồn tại trong danh sách URI được phép chuyển hướng", "Token not found, invalid accessToken": "Token không tìm thấy, accessToken không hợp lệ"}, "user": {"Display name cannot be empty": "<PERSON><PERSON>n hiển thị không thể trống", "MFA email is enabled but email is empty": "MFA email đã bật nh<PERSON>ng email trống", "MFA phone is enabled but phone number is empty": "MFA điện thoại đã bật nhưng số điện thoại trống", "New password cannot contain blank space.": "<PERSON><PERSON><PERSON> khẩu mới không thể chứa dấu trắng.", "the user's owner and name should not be empty": "chủ sở hữu và tên người dùng không được để trống"}, "util": {"No application is found for userId: %s": "<PERSON><PERSON><PERSON><PERSON> tìm thấy <PERSON>ng dụng cho ID người dùng: %s", "No provider for category: %s is found for application: %s": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nhà cung cấp cho danh mục: %s cho ứng dụng: %s", "The provider: %s is not found": "Nhà cung cấp: %s không đ<PERSON><PERSON><PERSON> tìm thấy"}, "verification": {"Invalid captcha provider.": "<PERSON><PERSON><PERSON> cung cấp cap<PERSON>a không hợp lệ.", "Phone number is invalid in your region %s": "Số điện tho<PERSON><PERSON> không hợp lệ trong vùng của bạn %s", "The verification code has already been used!": "<PERSON>ã xác thực đã được sử dụng!", "The verification code has not been sent yet!": "<PERSON>ã xác thực chưa đư<PERSON> g<PERSON>!", "Turing test failed.": "<PERSON><PERSON><PERSON> thất bại.", "Unable to get the email modify rule.": "<PERSON><PERSON><PERSON><PERSON> thể lấy quy tắc sửa đổi email.", "Unable to get the phone modify rule.": "<PERSON><PERSON><PERSON><PERSON> thể thay đổi quy tắc trên điện thoại.", "Unknown type": "<PERSON><PERSON><PERSON> không x<PERSON>c đ<PERSON>", "Wrong verification code!": "Mã xác thực sai!", "You should verify your code in %d min!": "Bạn nên kiểm tra mã của mình trong %d phút!", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "vui lòng thêm nhà cung cấp SMS vào danh sách \\\"Providers\\\" cho ứng dụng: %s", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "vui lòng thêm nhà cung cấp <PERSON>ail vào danh sách \\\"Providers\\\" cho ứng dụng: %s", "the user does not exist, please sign up first": "<PERSON><PERSON><PERSON>i dùng không tồn tại, vui lòng đăng ký trước"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "<PERSON>ui lòng gọi WebAuthnSigninBegin trước"}}