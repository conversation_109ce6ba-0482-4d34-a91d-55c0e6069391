{"account": {"Failed to add user": "Kullanıcı eklenemedi", "Get init score failed, error: %w": "Başlangıç puanı alınamadı, hata: %w", "Please sign out first": "Lütfen önce çıkış yapın", "The application does not allow to sign up new account": "Uygulama yeni hesap kaydına izin vermiyor"}, "auth": {"Challenge method should be S256": "Challenge yöntemi S256 olmalı", "DeviceCode Invalid": "Cihaz Kodu Geçersiz", "Failed to create user, user information is invalid: %s": "Kullanıcı oluşturulamadı, kullanıcı bilgileri geçersiz: %s", "Failed to login in: %s": "Giriş yapılamadı: %s", "Invalid token": "Geçersiz token", "State expected: %s, but got: %s": "Beklenen durum: %s, fakat <PERSON><PERSON>: %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "Provider: %s ve kullanıcı adı: %s (%s) için hesap mevcut değil ve %%s ile yeni hesap açılmasına izin verilmiyor, lütfen başka yöntemle kaydolun", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "Provider: %s ve kullanıcı adı: %s (%s) için hesap mevcut değil ve yeni hesap açılmasına izin verilmiyor, lütfen BT destek ile iletişime geçin", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "Provider: %s ve kullanıcı adı: %s (%s) zaten başka bir hesaba bağlı: %s (%s)", "The application: %s does not exist": "Uygulama: %s bulunamadı", "The login method: login with LDAP is not enabled for the application": "Uygulama için LDAP ile giriş yöntemi etkin değil", "The login method: login with SMS is not enabled for the application": "Uygulama için SMS ile giriş yöntemi etkin değil", "The login method: login with email is not enabled for the application": "Uygulama için e-posta ile giriş yöntemi etkin de<PERSON>", "The login method: login with face is not enabled for the application": "Uygulama için yüz ile giriş yöntemi etkin değil", "The login method: login with password is not enabled for the application": "<PERSON><PERSON><PERSON> ile giriş yöntemi bu uygulama i<PERSON><PERSON> et<PERSON> de<PERSON>", "The organization: %s does not exist": "Organizasyon: %s mevcut değil", "The provider: %s does not exist": "Sağlayıcı: %s mevcut değil", "The provider: %s is not enabled for the application": "Provider: %s bu uygu<PERSON><PERSON> i<PERSON><PERSON> et<PERSON>", "Unauthorized operation": "Yetkisiz işlem", "Unknown authentication type (not password or provider), form = %s": "Bilinmeyen kimlik doğrulama türü (şifre veya provider değil), form = %s", "User's tag: %s is not listed in the application's tags": "Kullanıcı etiketi: %s uygulamanın etiketlerinde listelenmiyor", "UserCode Expired": "Kullanıcı Kodu Süresi Doldu", "UserCode Invalid": "Kullanıcı Kodu Geçersiz", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "Ücretli kullanıcı %s aktif veya bekleyen bir aboneliğe sahip değil ve uygulama: %s varsayılan fiyatlandırmaya sahip değil", "the application for user %s is not found": "%s kullanıcısı için uygulama bulunamadı", "the organization: %s is not found": "Organizasyon: %s bulunamadı"}, "cas": {"Service %s and %s do not match": "Servis %s ve %s eşleşmiyor"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s, CIDR biçim gerekliliklerini karşılamıyor: %s", "Affiliation cannot be blank": "Kurum boş o<PERSON>az", "CIDR for IP: %s should not be empty": "IP için CIDR: %s boş olma<PERSON>ı", "Default code does not match the code's matching rules": "Varsay<PERSON><PERSON> kod, kodun eşleşme kurallarıyla uyuşmuyor", "DisplayName cannot be blank": "Görünen ad boş olamaz", "DisplayName is not valid real name": "Görünen ad geçerli bir gerçek ad değil", "Email already exists": "E-posta zaten var", "Email cannot be empty": "E-posta boş o<PERSON>az", "Email is invalid": "E-posta geçersiz", "Empty username.": "Kullanıcı adı boş.", "Face data does not exist, cannot log in": "<PERSON><PERSON>z verisi mev<PERSON> de<PERSON>, g<PERSON><PERSON>", "Face data mismatch": "Yüz verisi uyuşmazlığı", "Failed to parse client IP: %s": "İstemci IP'si ayrıştırılamadı: %s", "FirstName cannot be blank": "Ad boş olamaz", "Invitation code cannot be blank": "<PERSON><PERSON> kodu bo<PERSON> o<PERSON>az", "Invitation code exhausted": "<PERSON>t kodu kullanım dışı", "Invitation code is invalid": "<PERSON>t kodu geçersiz", "Invitation code suspended": "Davet kodu askıya alındı", "LDAP user name or password incorrect": "LDAP kullanıcı adı veya şifre yanlış", "LastName cannot be blank": "Soyad boş o<PERSON>az", "Multiple accounts with same uid, please check your ldap server": "<PERSON><PERSON><PERSON> uid'ye sahip birden fazla hesap, lütfen ldap sunucunuzu kontrol edin", "Organization does not exist": "Organizasyon bulunamadı", "Password cannot be empty": "<PERSON><PERSON><PERSON> bo<PERSON>", "Phone already exists": "Telefon numarası zaten var", "Phone cannot be empty": "Telefon numarası boş olamaz", "Phone number is invalid": "Telefon numarası geçersiz", "Please register using the email corresponding to the invitation code": "Lütfen davet koduna karşılık gelen e-posta ile kayıt olun", "Please register using the phone  corresponding to the invitation code": "Lütfen davet koduna karşılık gelen telefonla kayıt olun", "Please register using the username corresponding to the invitation code": "Lütfen davet koduna karşılık gelen kullanıcı adıyla kayıt olun", "Session outdated, please login again": "Oturum süresi doldu, lütfen tekrar giriş yapın", "The invitation code has already been used": "Davet kodu zaten kullanılmış", "The user is forbidden to sign in, please contact the administrator": "Kullanıcı giriş yapmaktan men edildi, lütfen yönetici ile iletişime geçin", "The user: %s doesn't exist in LDAP server": "Kullanıcı: %s LDAP sunucusunda mevcut değil", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "Kullanıcı adı yalnızca alfanümerik karakterler, alt çizgi veya tire içerebilir, ardışık tire veya alt çizgi içeremez ve tire veya alt çizgi ile başlayamaz veya bitemez.", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "Hesap alanı \\\"%s\\\" için \\\"%s\\\" de<PERSON><PERSON>, hesap ö<PERSON>esi regex'iyle <PERSON>", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "<PERSON><PERSON><PERSON> alanı \\\"%s\\\" için \\\"%s\\\" değ<PERSON>, \\\"%s\\\" uygulamasının kayıt öğesi regex'iyle <PERSON>", "Username already exists": "Kullanıcı adı zaten var", "Username cannot be an email address": "Kullanıcı adı e-posta adresi olamaz", "Username cannot contain white spaces": "Kullanıcı adı boşluk içeremez", "Username cannot start with a digit": "Kullanıcı adı rakamla başlayamaz", "Username is too long (maximum is 255 characters).": "Kullanıcı adı çok uzun (maksimum 255 karakter).", "Username must have at least 2 characters": "Kullanıcı adı en az 2 karakter olmalı", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "Kullanıcı adı e-posta biçimini destekler. Ayrıca kullanıcı adı yalnızca alfanümerik karakterler, alt çizgiler veya tireler içerebilir, ardışık tireler veya alt çizgiler olamaz ve tire veya alt çizgi ile başlayıp bitemez. Ayrıca e-posta biçimine dikkat edin.", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "Çok fazla hatalı şifre veya kod girdiniz, lütfen %d dakika bekleyin ve tekrar deneyin", "Your IP address: %s has been banned according to the configuration of: ": "IP adresiniz: %s, yapılandırmaya göre yasaklandı: ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "Şifrenizin süresi doldu. Lütfen \\\"Şifremi unuttum\\\"a tıklayarak şifrenizi sıfırlayın", "Your region is not allow to signup by phone": "Bölgeniz telefonla kayıt yapmaya izin verilmiyor", "password or code is incorrect": "şifre veya kod yanlış", "password or code is incorrect, you have %s remaining chances": "şifre veya kod yanlış, %s hakkınız kaldı", "unsupported password type: %s": "desteklenmeyen şifre türü: %s"}, "enforcer": {"the adapter: %s is not found": "bağdaştırıcı: %s bulunamadı"}, "general": {"Failed to import groups": "Gruplar içe aktarılamadı", "Failed to import users": "Kullanıcılar içe aktarılamadı", "Missing parameter": "Eksik parametre", "Only admin user can specify user": "Yalnızca yönetici kullanıcı kullanıcı belirleyebilir", "Please login first": "Lütfen önce giriş yapın", "The organization: %s should have one application at least": "Organizasyon: %s en az bir uygulamaya sahip olmalı", "The user: %s doesn't exist": "Kullanıcı: %s bulunamadı", "Wrong userId": "Yanlış kullanıcı kimliği", "don't support captchaProvider: ": "captcha<PERSON><PERSON><PERSON> desteklenmiyor: ", "this operation is not allowed in demo mode": "bu işlem demo modunda izin verilmiyor", "this operation requires administrator to perform": "bu işlem yönetici tarafından gerçekleştirilmelidir"}, "ldap": {"Ldap server exist": "LDAP sunucusu zaten var"}, "link": {"Please link first": "Lütfen önce bağlayın", "This application has no providers": "Bu uygulamanın provider'ı yok", "This application has no providers of type": "Bu uygulamanın bu türde provider'ı yok", "This provider can't be unlinked": "Bu provider bağlantısı kaldırılamaz", "You are not the global admin, you can't unlink other users": "Global yönetici değilsiniz, başka kullanıcıların bağlantısını kaldıramazsınız", "You can't unlink yourself, you are not a member of any application": "Kendinizin bağlantısını kaldıramazsınız, hiçbir uygulamanın üyesi değilsiniz"}, "organization": {"Only admin can modify the %s.": "Yalnızca yönetici %s değiştirebilir.", "The %s is immutable.": "%s değiştirilemez.", "Unknown modify rule %s.": "Bilinmeyen değiştirme kuralı %s.", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "'built-in' (yer<PERSON><PERSON><PERSON>) organizasyona yeni bir kullanıcı ekleme şu anda devre dışı bırakılmıştır. Not: 'built-in' organizasyonundaki tüm kullanıcılar Casdoor'da genel yöneticilerdir. Belgelere bakın: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. Hala 'built-in' organizasyonu için bir kullanıcı oluşturmak isterseniz, organizasyonun ayarları sayfasına gidin ve 'Yetki onayı var' seçeneğini etkinleştirin."}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "İzin: \\\"%s\\\" mevcut değil"}, "provider": {"Invalid application id": "Geçersiz uygulama id", "the provider: %s does not exist": "provider: %s bulunamadı"}, "resource": {"User is nil for tag: avatar": "Kullanıcı nil avatar etiketi için", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "Kullanıcı adı veya fullFilePath boş: username = %s, fullFilePath = %s"}, "saml": {"Application %s not found": "Uygulama %s bulunamadı"}, "saml_sp": {"provider %s's category is not SAML": "provider %s kategorisi SAML değil"}, "service": {"Empty parameters for emailForm: %v": "emailForm için boş parametreler: %v", "Invalid Email receivers: %s": "Geçersiz e-posta alıcıları: %s", "Invalid phone receivers: %s": "Geçersiz telefon alıcıları: %s"}, "storage": {"The objectKey: %s is not allowed": "objectKey: %s izin verilmiyor", "The provider type: %s is not supported": "provider türü: %s desteklenmiyor"}, "subscription": {"Error": "<PERSON><PERSON>"}, "token": {"Grant_type: %s is not supported in this application": "Grant_type: %s bu uygulamada desteklenmiyor", "Invalid application or wrong clientSecret": "Geçersiz uygulama veya yanlış clientSecret", "Invalid client_id": "Geçersiz client_id", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "Redirect URI: %s izin verilen Redirect URI listesinde yok", "Token not found, invalid accessToken": "Token bulunamadı, geçersiz accessToken"}, "user": {"Display name cannot be empty": "Görünen ad boş olamaz", "MFA email is enabled but email is empty": "MFA e-postası etkin ancak e-posta boş", "MFA phone is enabled but phone number is empty": "MFA telefonu etkin ancak telefon numarası boş", "New password cannot contain blank space.": "Yeni şifre boşluk içeremez.", "the user's owner and name should not be empty": "kullanıcının sahibi ve adı boş olmamalıdır"}, "util": {"No application is found for userId: %s": "KullanıcıId: %s için uygulama bulunamadı", "No provider for category: %s is found for application: %s": "%s kategorisi için provider bulunamadı uygulama: %s için", "The provider: %s is not found": "Provider: %s bulunamadı"}, "verification": {"Invalid captcha provider.": "Geçersiz captcha sağlayıcı.", "Phone number is invalid in your region %s": "Telefon numaranız bölgenizde geçersiz %s", "The verification code has already been used!": "Doğrulama kodu zaten kullanılmış!", "The verification code has not been sent yet!": "Doğrulama kodu henüz gönderilmedi!", "Turing test failed.": "Turing testi başarısız.", "Unable to get the email modify rule.": "E-posta değiştirme kuralı alınamıyor.", "Unable to get the phone modify rule.": "Telefon değiştirme kuralı alınamıyor.", "Unknown type": "Bilinmeyen tür", "Wrong verification code!": "Yanlış doğrulama kodu!", "You should verify your code in %d min!": "Kodunuzu %d dakika içinde doğrulamalısınız!", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "lütfen uygulama için \\\"Sağlayıcılar\\\" listesine bir SMS sağlayıcı ekleyin: %s", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "lütfen uygulama için \\\"Sağlayıcılar\\\" listesine bir E-posta sağlayıcı ekleyin: %s", "the user does not exist, please sign up first": "kullanıcı mevcut değil, lütfen önce kaydolun"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "Lütfen önce WebAuthnSigninBegin çağırın"}}