{"account": {"Failed to add user": "Aggiunta utente fallita", "Get init score failed, error: %w": "Errore iniziale punteggio: %w", "Please sign out first": "Esegui prima il logout", "The application does not allow to sign up new account": "L'app non consente registrazione"}, "auth": {"Challenge method should be S256": "Metodo challenge deve essere S256", "DeviceCode Invalid": "Codice dispositivo non valido", "Failed to create user, user information is invalid: %s": "Creazione utente fallita: dati non validi: %s", "Failed to login in: %s": "Login fallito: %s", "Invalid token": "Token non valido", "State expected: %s, but got: %s": "Stato atteso: %s, ricevuto: %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "Account per provider: %s e utente: %s (%s) non esiste, registrazione tramite %%s non consentita", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "Account per provider: %s e utente: %s (%s) non esiste, registrazione non consentita: contatta l'assistenza IT", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "Account per provider: %s e utente: %s (%s) già collegato a un altro account: %s (%s)", "The application: %s does not exist": "L'app: %s non esiste", "The login method: login with LDAP is not enabled for the application": "Metodo di accesso: login con LDAP non abilitato per l'applicazione", "The login method: login with SMS is not enabled for the application": "Metodo di accesso: login con SMS non abilitato per l'applicazione", "The login method: login with email is not enabled for the application": "Metodo di accesso: login con email non abilitato per l'applicazione", "The login method: login with face is not enabled for the application": "Metodo di accesso: login con riconoscimento facciale non abilitato per l'applicazione", "The login method: login with password is not enabled for the application": "Login con password non abilitato per questa app", "The organization: %s does not exist": "L'organizzazione: %s non esiste", "The provider: %s does not exist": "Il provider: %s non esiste", "The provider: %s is not enabled for the application": "Il provider: %s non è abilitato per l'app", "Unauthorized operation": "Operazione non autorizzata", "Unknown authentication type (not password or provider), form = %s": "Tipo di autenticazione sconosciuto (non password o provider), form = %s", "User's tag: %s is not listed in the application's tags": "Il tag dell'utente: %s non è presente nei tag dell'applicazione", "UserCode Expired": "Codice utente scaduto", "UserCode Invalid": "Codice utente non valido", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "L'utente a pagamento %s non ha una sottoscrizione attiva o in attesa e l'applicazione: %s non ha una tariffazione predefinita", "the application for user %s is not found": "applicazione per l'utente %s non trovata", "the organization: %s is not found": "organizzazione: %s non trovata"}, "cas": {"Service %s and %s do not match": "Servizi %s e %s non corrispondono"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s non soddisfa i requisiti di formato CIDR: %s", "Affiliation cannot be blank": "Affiliazione obbligatoria", "CIDR for IP: %s should not be empty": "Il CIDR per l'IP: %s non deve essere vuoto", "Default code does not match the code's matching rules": "Il codice predefinito non rispetta le regole di corrispondenza del codice", "DisplayName cannot be blank": "Nome visualizzato obbligatorio", "DisplayName is not valid real name": "Nome visualizzato non valido", "Email already exists": "Email gi<PERSON> esistente", "Email cannot be empty": "Email obbliga<PERSON>", "Email is invalid": "Email non valida", "Empty username.": "<PERSON><PERSON><PERSON> vuoto", "Face data does not exist, cannot log in": "Dati facciali assenti, impossibile effettuare il login", "Face data mismatch": "Mancata corrispondenza dei dati facciali", "Failed to parse client IP: %s": "Impossibile analizzare l'IP del client: %s", "FirstName cannot be blank": "Nome obbligatorio", "Invitation code cannot be blank": "Il codice di invito non può essere vuoto", "Invitation code exhausted": "Codice di invito es<PERSON>rito", "Invitation code is invalid": "Codice di invito non valido", "Invitation code suspended": "Codice di invito sospeso", "LDAP user name or password incorrect": "LDAP username o password errati", "LastName cannot be blank": "Cognome obbligatorio", "Multiple accounts with same uid, please check your ldap server": "UID duplicato, verifica il server LDAP", "Organization does not exist": "Organizzazione inesistente", "Password cannot be empty": "La password non può essere vuota", "Phone already exists": "Telefono già esistente", "Phone cannot be empty": "Telefono obbligatorio", "Phone number is invalid": "Numero telefono non valido", "Please register using the email corresponding to the invitation code": "Registrati con l'email corrispondente al codice di invito", "Please register using the phone  corresponding to the invitation code": "Registrati con il numero di telefono corrispondente al codice di invito", "Please register using the username corresponding to the invitation code": "Registrati con il nome utente corrispondente al codice di invito", "Session outdated, please login again": "Sessione scaduta, rieffettua il login", "The invitation code has already been used": "Il codice di invito è già stato utilizzato", "The user is forbidden to sign in, please contact the administrator": "Utente bloccato, contatta l'amministratore", "The user: %s doesn't exist in LDAP server": "L'utente: %s non esiste nel server LDAP", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "Username solo caratteri alfanumerici, underscore o trattini. Non può iniziare/finire con trattino o underscore.", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "Il valore \\\"%s\\\" per il campo account \\\"%s\\\" non corrisponde alla regex dell'elemento account", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "Il valore \\\"%s\\\" per il campo registrazione \\\"%s\\\" non corrisponde alla regex dell'elemento registrazione dell'applicazione \\\"%s\\\"", "Username already exists": "Username già esistente", "Username cannot be an email address": "Username non può essere un'email", "Username cannot contain white spaces": "Username non può contenere spazi", "Username cannot start with a digit": "Username non può iniziare con un numero", "Username is too long (maximum is 255 characters).": "<PERSON><PERSON><PERSON> troppo lungo (max 255 caratteri)", "Username must have at least 2 characters": "Username minimo 2 caratteri", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "Il nome utente supporta il formato email. Inoltre il nome utente può contenere solo caratteri alfanumerici, underscore o trattini, non può avere trattini o underscore consecutivi e non può iniziare o terminare con un trattino o underscore. Presta attenzione al formato email.", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "<PERSON><PERSON><PERSON> tentativi errati, attendi %d minuti", "Your IP address: %s has been banned according to the configuration of: ": "Il tuo indirizzo IP: %s è stato bannato secondo la configurazione di: ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "La tua password è scaduta. Reimposta la password cliccando \\\"Password dimenticata\\\"", "Your region is not allow to signup by phone": "Registrazione via telefono non consentita nella tua regione", "password or code is incorrect": "password o codice errati", "password or code is incorrect, you have %s remaining chances": "password o codice errati, %s tentativi rimasti", "unsupported password type: %s": "tipo password non supportato: %s"}, "enforcer": {"the adapter: %s is not found": "l'adapter: %s non è stato trovato"}, "general": {"Failed to import groups": "Impossibile importare i gruppi", "Failed to import users": "Importazione utenti fallita", "Missing parameter": "Parametro mancante", "Only admin user can specify user": "Solo un utente amministratore può specificare l'utente", "Please login first": "Effettua prima il login", "The organization: %s should have one application at least": "L'organizzazione: %s deve avere almeno un'applicazione", "The user: %s doesn't exist": "Utente: %s non esiste", "Wrong userId": "ID utente errato", "don't support captchaProvider: ": "captchaProvider non supportato: ", "this operation is not allowed in demo mode": "questa operazione non è consentita in modalità demo", "this operation requires administrator to perform": "questa operazione richiede un amministratore"}, "ldap": {"Ldap server exist": "Server LDAP esistente"}, "link": {"Please link first": "Collega prima", "This application has no providers": "L'app non ha provider", "This application has no providers of type": "L'app non ha provider di tipo", "This provider can't be unlinked": "Questo provider non può essere scollegato", "You are not the global admin, you can't unlink other users": "Non sei admin globale, non puoi scollegare altri utenti", "You can't unlink yourself, you are not a member of any application": "Non puoi scollegarti, non sei membro di alcuna app"}, "organization": {"Only admin can modify the %s.": "Solo admin può modificare %s.", "The %s is immutable.": "%s è immutabile.", "Unknown modify rule %s.": "Regola modifica %s sconosciuta.", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "L'aggiunta di un nuovo utente all'organizzazione 'built-in' (integrata) è attualmente disabilitata. Si noti che tutti gli utenti nell'organizzazione 'built-in' sono amministratori globali in Casdoor. Consultare la documentazione: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. Se si desidera comunque creare un utente per l'organizzazione 'built-in', andare alla pagina delle impostazioni dell'organizzazione e abilitare l'opzione 'Ha il consenso ai privilegi'."}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "Il permesso: \\\"%s\\\" non esiste"}, "provider": {"Invalid application id": "ID app non valido", "the provider: %s does not exist": "provider: %s non esiste"}, "resource": {"User is nil for tag: avatar": "Utente nullo per tag: avatar", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "Username o percorso file vuoti: username = %s, fullFilePath = %s"}, "saml": {"Application %s not found": "App %s non trovata"}, "saml_sp": {"provider %s's category is not SAML": "Provider %s non è di tipo SAML"}, "service": {"Empty parameters for emailForm: %v": "Parametri emailForm vuoti: %v", "Invalid Email receivers: %s": "Destinatari email non validi: %s", "Invalid phone receivers: %s": "Destinatari SMS non validi: %s"}, "storage": {"The objectKey: %s is not allowed": "<PERSON>ave oggetto: %s non consentita", "The provider type: %s is not supported": "Tipo provider: %s non supportato"}, "subscription": {"Error": "Errore"}, "token": {"Grant_type: %s is not supported in this application": "Grant_type: %s non supportato in questa app", "Invalid application or wrong clientSecret": "App non valida o clientSecret errato", "Invalid client_id": "client_id non valido", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "URI di redirect: %s non consentito", "Token not found, invalid accessToken": "Token non trovato, accessToken non valido"}, "user": {"Display name cannot be empty": "Nome visualizzato obbligatorio", "MFA email is enabled but email is empty": "L'email MFA è abilitata ma l'email è vuota", "MFA phone is enabled but phone number is empty": "Il telefono MFA è abilitato ma il numero di telefono è vuoto", "New password cannot contain blank space.": "Nuova password non può contenere spazi", "the user's owner and name should not be empty": "il proprietario e il nome dell'utente non devono essere vuoti"}, "util": {"No application is found for userId: %s": "Nessuna app trovata per userId: %s", "No provider for category: %s is found for application: %s": "Nessun provider per categoria: %s nell'app: %s", "The provider: %s is not found": "Provider: %s non trovato"}, "verification": {"Invalid captcha provider.": "Provider captcha non valido", "Phone number is invalid in your region %s": "Numero telefono non valido nella regione %s", "The verification code has already been used!": "Il codice di verifica è già stato utilizzato!", "The verification code has not been sent yet!": "Il codice di verifica non è ancora stato inviato!", "Turing test failed.": "Test Turing fallito", "Unable to get the email modify rule.": "Impossibile ottenere regola modifica email", "Unable to get the phone modify rule.": "Impossibile ottenere regola modifica telefono", "Unknown type": "<PERSON><PERSON><PERSON>", "Wrong verification code!": "Codice verifica errato!", "You should verify your code in %d min!": "Verifica codice entro %d min!", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "aggiungi un provider SMS all'elenco \\\"Providers\\\" per l'applicazione: %s", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "aggiungi un provider Email all'elenco \\\"Providers\\\" per l'applicazione: %s", "the user does not exist, please sign up first": "Utente inesistente, registrati prima"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "Chiamare prima WebAuthnSigninBegin"}}