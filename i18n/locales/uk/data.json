{"account": {"Failed to add user": "Не вдалося додати користувача", "Get init score failed, error: %w": "Не вдалося отримати початковий бал, помилка: %w", "Please sign out first": "Спочатку вийдіть із системи", "The application does not allow to sign up new account": "Додаток не дозволяє реєструвати нові облікові записи"}, "auth": {"Challenge method should be S256": "Метод виклику має бути S256", "DeviceCode Invalid": "Недійсний DeviceCode", "Failed to create user, user information is invalid: %s": "Не вдалося створити користувача, інформація недійсна: %s", "Failed to login in: %s": "Не вдалося увійти: %s", "Invalid token": "Недійсний токен", "State expected: %s, but got: %s": "Очікувалося стан: %s, але отримано: %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "Обліковий запис для провайдера: %s та імені користувача: %s (%s) не існує і не дозволяється реєструвати як новий через %%s, використайте інший спосіб", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "Обліковий запис для провайдера: %s та імені користувача: %s (%s) не існує і не дозволяється реєструвати як новий, зверніться до IT-підтримки", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "Обліковий запис для провайдера: %s та імені користувача: %s (%s) уже пов’язаний з іншим обліковим записом: %s (%s)", "The application: %s does not exist": "Додаток: %s не існує", "The login method: login with LDAP is not enabled for the application": "Метод входу через LDAP не увімкнено для цього додатка", "The login method: login with SMS is not enabled for the application": "Метод входу через SMS не увімкнено для цього додатка", "The login method: login with email is not enabled for the application": "Метод входу через email не увімкнено для цього додатка", "The login method: login with face is not enabled for the application": "Метод входу через обличчя не увімкнено для цього додатка", "The login method: login with password is not enabled for the application": "Метод входу через пароль не увімкнено для цього додатка", "The organization: %s does not exist": "Організація: %s не існує", "The provider: %s does not exist": "Провайдер: %s не існує", "The provider: %s is not enabled for the application": "Провайдер: %s не увімкнено для цього додатка", "Unauthorized operation": "Неавторизована операція", "Unknown authentication type (not password or provider), form = %s": "Невідомий тип автентифікаці<PERSON> (не пароль і не провайдер), форма = %s", "User's tag: %s is not listed in the application's tags": "Тег користувача: %s відсутній у тегах додатка", "UserCode Expired": "UserCode застарів", "UserCode Invalid": "UserCode недійсний", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "Користувач %s не має активної або очікуваної підписки, а додаток: %s не має типової ціни", "the application for user %s is not found": "Додаток для користувача %s не знайдено", "the organization: %s is not found": "Організація: %s не знайдена"}, "cas": {"Service %s and %s do not match": "Сервіс %s і %s не збігаються"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s не відповідає формату CIDR: %s", "Affiliation cannot be blank": "Приналежність не може бути порожньою", "CIDR for IP: %s should not be empty": "CIDR для IP: %s не повинен бути порожнім", "Default code does not match the code's matching rules": "Код за замовчуванням не відповідає правилам відповідності", "DisplayName cannot be blank": "Відображуване ім’я не може бути порожнім", "DisplayName is not valid real name": "Відображуване ім’я не є дійсним справжнім ім’ям", "Email already exists": "Email уже існує", "Email cannot be empty": "Email не може бути порожнім", "Email is invalid": "Email недійсний", "Empty username.": "Порожнє ім’я користувача.", "Face data does not exist, cannot log in": "Дані обличчя відсутні, неможливо увійти", "Face data mismatch": "Невідповідність даних обличчя", "Failed to parse client IP: %s": "Не вдалося розібрати IP клієнта: %s", "FirstName cannot be blank": "Ім’я не може бути порожнім", "Invitation code cannot be blank": "Код запрошення не може бути порожнім", "Invitation code exhausted": "Код запрошення вичерпано", "Invitation code is invalid": "Код запрошення недійсний", "Invitation code suspended": "Код запрошення призупинено", "LDAP user name or password incorrect": "Ім’я користувача або пароль LDAP неправильні", "LastName cannot be blank": "Прізвище не може бути порожнім", "Multiple accounts with same uid, please check your ldap server": "Кілька облікових записів з однаковим uid, перевірте ваш ldap-сервер", "Organization does not exist": "Організація не існує", "Password cannot be empty": "Пароль не може бути порожнім", "Phone already exists": "Телефон уже існує", "Phone cannot be empty": "Телефон не може бути порожнім", "Phone number is invalid": "Номер телефону недійсний", "Please register using the email corresponding to the invitation code": "Будь ласка, зареєструйтесь, використовуючи email, що відповідає коду запрошення", "Please register using the phone  corresponding to the invitation code": "Будь ласка, зареєструйтесь, використовуючи телефон, що відповідає коду запрошення", "Please register using the username corresponding to the invitation code": "Будь ласка, зареєструйтесь, використовуючи ім’я користувача, що відповідає коду запрошення", "Session outdated, please login again": "Сесію застаро, будь ласка, увійдіть знову", "The invitation code has already been used": "Код запрошення вже використано", "The user is forbidden to sign in, please contact the administrator": "Користувачу заборонено вхід, зверніться до адміністратора", "The user: %s doesn't exist in LDAP server": "Користувач: %s не існує на сервері LDAP", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "Ім’я користувача може містити лише буквено-цифрові символи, підкреслення або дефіси, не може мати послідовні дефіси або підкреслення та не може починатися або закінчуватися дефісом або підкресленням.", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "Värdet \\\"%s\\\" för kontofältet \\\"%s\\\" matchar inte kontots regex", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "Värdet \\\"%s\\\" för registreringsfältet \\\"%s\\\" matchar inte registreringsfältets regex för applikationen \\\"%s\\\"", "Username already exists": "Ім’я користувача вже існує", "Username cannot be an email address": "Ім’я користувача не може бути email-адресою", "Username cannot contain white spaces": "Ім’я користувача не може містити пробіли", "Username cannot start with a digit": "Ім’я користувача не може починатися з цифри", "Username is too long (maximum is 255 characters).": "Ім’я користувача занадто довге (максимум 255 символів).", "Username must have at least 2 characters": "Ім’я користувача має містити щонайменше 2 символи", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "Ім’я користувача підтримує формат email. Також може містити лише буквено-цифрові символи, підкреслення або дефіси, не може мати послідовні дефіси або підкреслення та не може починатися або закінчуватися дефісом або підкресленням. Зверніть увагу на формат email.", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "Ви ввели неправильний пароль або код забагато разів, зачекайте %d хвилин і спробуйте знову", "Your IP address: %s has been banned according to the configuration of: ": "Ваша IP-адреса: %s заблокована відповідно до конфігурації: ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "<PERSON><PERSON> lösenord har gått ut. <PERSON><PERSON>täll det genom att klicka på \\\"Glömt lösenord\\\"", "Your region is not allow to signup by phone": "У вашому регіоні реєстрація за телефоном недоступна", "password or code is incorrect": "пароль або код неправильний", "password or code is incorrect, you have %s remaining chances": "пароль або код неправильний, у вас залишилось %s спроб", "unsupported password type: %s": "непідтримуваний тип пароля: %s"}, "enforcer": {"the adapter: %s is not found": "адаптер: %s не знайдено"}, "general": {"Failed to import groups": "Не вдалося імпортувати групи", "Failed to import users": "Не вдалося імпортувати користувачів", "Missing parameter": "Відсутн<PERSON>й параметр", "Only admin user can specify user": "Лише адміністратор може вказати користувача", "Please login first": "Спочатку увійдіть", "The organization: %s should have one application at least": "Організація: %s має мати щонайменше один додаток", "The user: %s doesn't exist": "Користувач: %s не існує", "Wrong userId": "Неправильний userId", "don't support captchaProvider: ": "не підтримується captchaProvider: ", "this operation is not allowed in demo mode": "ця операція недоступна в демо-режимі", "this operation requires administrator to perform": "ця операція потребує прав адміністратора"}, "ldap": {"Ldap server exist": "Сервер LDAP існує"}, "link": {"Please link first": "Спочатку виконайте прив’язку", "This application has no providers": "Цей додаток не має провайдерів", "This application has no providers of type": "Цей додаток не має провайдерів цього типу", "This provider can't be unlinked": "Цього провайдера не можна від’єднати", "You are not the global admin, you can't unlink other users": "Ви не глобальний адміністратор, не можете від’єднувати інших користувачів", "You can't unlink yourself, you are not a member of any application": "Ви не можете від’єднати себе, ви не є учасником жодного додатка"}, "organization": {"Only admin can modify the %s.": "Лише адміністратор може змінити %s.", "The %s is immutable.": "%s незмінний.", "Unknown modify rule %s.": "Невідоме правило зміни %s.", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "Додавання нового користувача до організації «built-in» (вбудованої) на даний момент вимкнено. Зауважте: усі користувачі в організації «built-in» є глобальними адміністраторами в Casdoor. Дивіться документацію: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. Якщо ви все ще хочете створити користувача для організації «built-in», перейдіть на сторінку налаштувань організації та увімкніть опцію «Має згоду на привілеї»."}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "Behörigheten: \\\"%s\\\" finns inte"}, "provider": {"Invalid application id": "Недійсний id додатка", "the provider: %s does not exist": "провайдер: %s не існує"}, "resource": {"User is nil for tag: avatar": "Користувач nil для тегу: avatar", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "Ім’я користувача або fullFilePath порожні: username = %s, fullFilePath = %s"}, "saml": {"Application %s not found": "Додаток %s не знайдено"}, "saml_sp": {"provider %s's category is not SAML": "категорія провайдера %s не є SAML"}, "service": {"Empty parameters for emailForm: %v": "Порожні параметри для emailForm: %v", "Invalid Email receivers: %s": "Недійсні отримувачі Email: %s", "Invalid phone receivers: %s": "Недійсні отримувачі телефону: %s"}, "storage": {"The objectKey: %s is not allowed": "objectKey: %s не дозволено", "The provider type: %s is not supported": "Тип провайдера: %s не підтримується"}, "subscription": {"Error": "Помилка"}, "token": {"Grant_type: %s is not supported in this application": "Grant_type: %s не підтримується в цьому додатку", "Invalid application or wrong clientSecret": "Недійсний додаток або неправильний clientSecret", "Invalid client_id": "Недійсний client_id", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "Redirect URI: %s відсутній у списку дозволених", "Token not found, invalid accessToken": "Токен не знайдено, недійсний accessToken"}, "user": {"Display name cannot be empty": "Відображуване ім’я не може бути порожнім", "MFA email is enabled but email is empty": "MFA email увімкнено, але email порожній", "MFA phone is enabled but phone number is empty": "MFA телефон увімкнено, але номер телефону порожній", "New password cannot contain blank space.": "Новий пароль не може містити пробіли.", "the user's owner and name should not be empty": "власник ім’я користувача не повинні бути порожніми"}, "util": {"No application is found for userId: %s": "Не знайдено додаток для userId: %s", "No provider for category: %s is found for application: %s": "Не знайдено провайдера категорії: %s для додатка: %s", "The provider: %s is not found": "Провайдер: %s не знайдено"}, "verification": {"Invalid captcha provider.": "Недійсний провайдер captcha.", "Phone number is invalid in your region %s": "Номер телефону недійсний у вашому регіоні %s", "The verification code has already been used!": "Код підтвердження вже використано!", "The verification code has not been sent yet!": "Код підтвердження ще не надіслано!", "Turing test failed.": "Тест Тюрінга не пройдено.", "Unable to get the email modify rule.": "Не вдалося отримати правило зміни email.", "Unable to get the phone modify rule.": "Не вдалося отримати правило зміни телефону.", "Unknown type": "Невідомий тип", "Wrong verification code!": "Неправильний код підтвердження!", "You should verify your code in %d min!": "Ви маєте підтвердити код за %d хв!", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "lägg till en SMS-leverantör i listan \\\"Leverantörer\\\" för applikationen: %s", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "lägg till en e-postleverantör i listan \\\"Leverantörer\\\" för applikationen: %s", "the user does not exist, please sign up first": "користувача не існує, спочатку зареєструйтесь"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "Спочатку викличте WebAuthnSigninBegin"}}