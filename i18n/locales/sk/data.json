{"account": {"Failed to add user": "Nepodarilo sa pridať používateľa", "Get init score failed, error: %w": "Získanie počiatočného skóre zlyhalo, chyba: %w", "Please sign out first": "Najskôr sa prosím odhlás<PERSON>", "The application does not allow to sign up new account": "Aplikácia neumožňuje registráciu nového <PERSON>tu"}, "auth": {"Challenge method should be S256": "Metóda výzvy by mala byť S256", "DeviceCode Invalid": "DeviceCode je neplat<PERSON>ý", "Failed to create user, user information is invalid: %s": "Nepodarilo sa vytvoriť používateľa, informácie o používateľovi sú neplatné: %s", "Failed to login in: %s": "Prihlásenie zlyhalo: %s", "Invalid token": "Neplatný token", "State expected: %s, but got: %s": "Očakávaný stav: %s, ale dostali sme: %s", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account via %%s, please use another way to sign up": "Účet pre poskytovateľa: %s a používateľské meno: %s (%s) neexistuje a nie je povolené zaregistrovať nový účet cez %%s, prosím použite iný spôsob registrácie", "The account for provider: %s and username: %s (%s) does not exist and is not allowed to sign up as new account, please contact your IT support": "Účet pre poskytovateľa: %s a používateľské meno: %s (%s) neexistuje a nie je povolené zaregistrovať nový účet, prosím kontaktujte vašu IT podporu", "The account for provider: %s and username: %s (%s) is already linked to another account: %s (%s)": "Účet pre poskytovateľa: %s a používateľské meno: %s (%s) je už prepojený s iným účtom: %s (%s)", "The application: %s does not exist": "Aplikácia: %s neexistuje", "The login method: login with LDAP is not enabled for the application": "Metóda prihlásenia: prihlásenie pomocou LDAP nie je pre aplikáciu povolená", "The login method: login with SMS is not enabled for the application": "Metóda prihlásenia: prihl<PERSON><PERSON>e pomocou SMS nie je pre aplikáciu povolená", "The login method: login with email is not enabled for the application": "Metóda prihlásenia: prihl<PERSON><PERSON>e pomocou e-mailu nie je pre aplikáciu povolená", "The login method: login with face is not enabled for the application": "Metóda prihlásenia: prihl<PERSON><PERSON>e pomocou tváre nie je pre aplikáciu povolená", "The login method: login with password is not enabled for the application": "Metóda prihlásenia: prihl<PERSON><PERSON>e pomocou hesla nie je pre aplikáciu povolená", "The organization: %s does not exist": "Organizácia: %s neexistuje", "The provider: %s does not exist": "Poskytovatel: %s neexistuje", "The provider: %s is not enabled for the application": "Poskytovateľ: %s nie je pre aplikáciu povolený", "Unauthorized operation": "Neautorizovaná operácia", "Unknown authentication type (not password or provider), form = %s": "Neznámy typ autentifik<PERSON> (nie heslo alebo posky<PERSON>ľ), forma = %s", "User's tag: %s is not listed in the application's tags": "Štítok používateľa: %s nie je uvedený v štítkoch aplikácie", "UserCode Expired": "UserCode je expirovaný", "UserCode Invalid": "UserCode je neplat<PERSON>ý", "paid-user %s does not have active or pending subscription and the application: %s does not have default pricing": "platiaci používateľ %s nemá aktívne alebo čakajúce predplatné a aplikácia: %s nemá predvolenú cenovú politiku", "the application for user %s is not found": "aplikácia pre používateľa %s nebola nájdená", "the organization: %s is not found": "organizácia: %s nebola nájdená"}, "cas": {"Service %s and %s do not match": "Služba %s a %s sa nezhodujú"}, "check": {"%s does not meet the CIDR format requirements: %s": "%s nevyhovuje požiadavkám formátu CIDR: %s", "Affiliation cannot be blank": "Príslušnosť nemôže byť prázdna", "CIDR for IP: %s should not be empty": "CIDR pre IP: %s nesmie byť prázdny", "Default code does not match the code's matching rules": "Predvolený kód nezodpovedá pravidlám zodpovedania kódu", "DisplayName cannot be blank": "Zobrazované meno nemô<PERSON>e byť prázdne", "DisplayName is not valid real name": "Zobrazované meno nie je platné skutočné meno", "Email already exists": "E-mail už existuje", "Email cannot be empty": "E-mail nemôže byť prázdny", "Email is invalid": "E-mail je ne<PERSON>", "Empty username.": "Prázdne používateľské meno.", "Face data does not exist, cannot log in": "Dáta o tvári neexistujú, nemožno sa prihlásiť", "Face data mismatch": "Nesúlad dát o tvári", "Failed to parse client IP: %s": "Zlyhalo parsovanie IP adresy klienta: %s", "FirstName cannot be blank": "<PERSON><PERSON> byť prázdne", "Invitation code cannot be blank": "Kód pozvania nemôže byť prázdny", "Invitation code exhausted": "<PERSON><PERSON>d poz<PERSON>a bol vyčerpaný", "Invitation code is invalid": "<PERSON><PERSON><PERSON> je ne<PERSON>", "Invitation code suspended": "<PERSON><PERSON><PERSON> poz<PERSON>a bol pozastavený", "LDAP user name or password incorrect": "LDAP používateľské meno alebo heslo sú nesprávne", "LastName cannot be blank": "Priezvisko nemôže byť prázdne", "Multiple accounts with same uid, please check your ldap server": "<PERSON><PERSON><PERSON> s rovnakým uid, skontrolujte svoj ldap server", "Organization does not exist": "Organizácia neexistuje", "Password cannot be empty": "<PERSON><PERSON><PERSON> byť prázdne", "Phone already exists": "Telefón už existuje", "Phone cannot be empty": "Telefón nemôže byť prázdny", "Phone number is invalid": "Telefónne číslo je neplatné", "Please register using the email corresponding to the invitation code": "Prosím, zaregistrujte sa pomocou e-mailu zodpovedajúceho kódu pozvania", "Please register using the phone  corresponding to the invitation code": "Prosím zaregistrujte se pomocí telefonu odpovídajícího p<PERSON> kódu", "Please register using the username corresponding to the invitation code": "Prosím, zaregistrujte sa pomocou používateľského mena zodpovedajúceho kódu pozvania", "Session outdated, please login again": "<PERSON><PERSON><PERSON><PERSON> je z<PERSON>, <PERSON>s<PERSON><PERSON>, p<PERSON><PERSON><PERSON>te sa <PERSON>nova", "The invitation code has already been used": "<PERSON><PERSON>d poz<PERSON> už bol použitý", "The user is forbidden to sign in, please contact the administrator": "Používateľovi je zak<PERSON>, prosím, kontaktujte administrátora", "The user: %s doesn't exist in LDAP server": "Používateľ: %s neexistuje na LDAP serveri", "The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline.": "Používateľské meno môže obsahovať iba alfanumerické znaky, podtržníky alebo pomlčky, nemôže obsahovať po sebe idúce pomlčky alebo podtržníky a nemôže začínať alebo končiť pomlčkou alebo podtržníkom.", "The value \\\"%s\\\" for account field \\\"%s\\\" doesn't match the account item regex": "Hodnota \\\"%s\\\" pre pole účtu \\\"%s\\\" nezodpovedá regulárnemu výrazu položky účtu", "The value \\\"%s\\\" for signup field \\\"%s\\\" doesn't match the signup item regex of the application \\\"%s\\\"": "Hodnota \\\"%s\\\" pre pole registrácie \\\"%s\\\" nezodpovedá regulárnemu výrazu položky registrácie aplikácie \\\"%s\\\"", "Username already exists": "Používateľské meno už existuje", "Username cannot be an email address": "Používateľské meno nemô<PERSON>e byť e-mailová adresa", "Username cannot contain white spaces": "Používateľské meno nemôže obsahovať medzery", "Username cannot start with a digit": "Používateľské meno nemôže začínať číslicou", "Username is too long (maximum is 255 characters).": "Používateľské meno je príliš dlhé (maximum je 255 znakov).", "Username must have at least 2 characters": "Používateľské meno musí mať aspoň 2 znaky", "Username supports email format. Also The username may only contain alphanumeric characters, underlines or hyphens, cannot have consecutive hyphens or underlines, and cannot begin or end with a hyphen or underline. Also pay attention to the email format.": "Používateľské meno podporuje formát e-mailu. Okrem toho môže používateľské meno obsahovať iba alfanumerické znaky, podčiarkovníky alebo pomlčky, nemô<PERSON>e mať po sebe idúce pomlčky alebo podčiarkovníky a nemôže začínať alebo končiť pomlčkou alebo podčiarkovníkom. Dajte tiež pozor na formát e-mailu.", "You have entered the wrong password or code too many times, please wait for %d minutes and try again": "Zadali ste nesprávne he<PERSON>lo alebo kód pr<PERSON><PERSON><PERSON> ve<PERSON> kr<PERSON>, prosím, počkajte %d minút a skúste to znova", "Your IP address: %s has been banned according to the configuration of: ": "Vaša IP adresa: %s bola zablokovaná podľa konfigurácie: ", "Your password has expired. Please reset your password by clicking \\\"Forgot password\\\"": "<PERSON><PERSON><PERSON><PERSON> heslo je expirované. Prosím, resetujte svoje heslo k<PERSON>nutím na \\\"Zabudnuté heslo\\\"", "Your region is not allow to signup by phone": "<PERSON><PERSON>š región neumožňuje registráciu cez telefón", "password or code is incorrect": "heslo alebo kód je nesp<PERSON>ávne", "password or code is incorrect, you have %s remaining chances": "he<PERSON><PERSON> alebo kód je <PERSON>, máte %s zostávajúcich pokusov", "unsupported password type: %s": "nepodporovaný typ hesla: %s"}, "enforcer": {"the adapter: %s is not found": "adaptér: %s nebol n<PERSON>"}, "general": {"Failed to import groups": "Import s<PERSON><PERSON>", "Failed to import users": "Nepodarilo sa importovať používateľov", "Missing parameter": "Chýbajúci parameter", "Only admin user can specify user": "Iba administrátor môže určiť používateľa", "Please login first": "Najskôr sa prosím prihláste", "The organization: %s should have one application at least": "Organizácia: %s by mala ma<PERSON> aspoň jednu aplikáciu", "The user: %s doesn't exist": "Používateľ: %s neexistuje", "Wrong userId": "Nesprávne ID používateľa", "don't support captchaProvider: ": "nepod<PERSON><PERSON><PERSON>: ", "this operation is not allowed in demo mode": "táto operácia nie je povolená v demo režime", "this operation requires administrator to perform": "táto operácia vyžaduje vykonanie administrátorom"}, "ldap": {"Ldap server exist": "LDAP server existuje"}, "link": {"Please link first": "Najskôr sa prosím prepojte", "This application has no providers": "<PERSON><PERSON><PERSON> nemá žiadnych poskytov<PERSON>ov", "This application has no providers of type": "<PERSON><PERSON><PERSON> a<PERSON> nemá poskytovateľov typu", "This provider can't be unlinked": "<PERSON><PERSON> pos<PERSON><PERSON> nemôže by<PERSON> odpojený", "You are not the global admin, you can't unlink other users": "<PERSON>e ste globálny administrá<PERSON>, nemôžete odpojiť iných používateľov", "You can't unlink yourself, you are not a member of any application": "Nemôžete sa odpojiť, nie ste členom žiadnej aplikácie"}, "organization": {"Only admin can modify the %s.": "Len administrátor môže upravovať %s.", "The %s is immutable.": "%s je ne<PERSON>.", "Unknown modify rule %s.": "Neznáme pravidlo úprav %s.", "adding a new user to the 'built-in' organization is currently disabled. Please note: all users in the 'built-in' organization are global administrators in Casdoor. Refer to the docs: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. If you still wish to create a user for the 'built-in' organization, go to the organization's settings page and enable the 'Has privilege consent' option.": "Pridávanie nového používateľa do organizácie „built-in' (vložená) je momentálne zakázané. Všimnite si, že všetci používatelia v organizácii „built-in' sú globálni správcovia v Casdoor. Pozrite si dokumentáciu: https://casdoor.org/docs/basic/core-concepts#how-does-casdoor-manage-itself. Ak stále chcete vytvoriť používateľa pre organizáciu „built-in', prejdite na stránku nastavení organizácie a povoľte možnosť „Má súhlas s privilégiami'."}, "permission": {"The permission: \\\"%s\\\" doesn't exist": "Povolenie: \\\"%s\\\" neexistuje"}, "provider": {"Invalid application id": "Neplatné id aplikácie", "the provider: %s does not exist": "poskytovateľ: %s neexistuje"}, "resource": {"User is nil for tag: avatar": "Používateľ je nil pre tag: avatar", "Username or fullFilePath is empty: username = %s, fullFilePath = %s": "Používateľské meno alebo fullFilePath je prázdny: používateľské meno = %s, fullFilePath = %s"}, "saml": {"Application %s not found": "Aplikácia %s nebola nájdená"}, "saml_sp": {"provider %s's category is not SAML": "kategória poskytovateľa %s nie je SAML"}, "service": {"Empty parameters for emailForm: %v": "Prázdne parametre pre emailForm: %v", "Invalid Email receivers: %s": "Neplatní príjemcovia e-mailu: %s", "Invalid phone receivers: %s": "Neplatní príjemcovia telefónu: %s"}, "storage": {"The objectKey: %s is not allowed": "objectKey: %s nie je povolený", "The provider type: %s is not supported": "Typ poskytovateľa: %s nie je podporovaný"}, "subscription": {"Error": "Chyba"}, "token": {"Grant_type: %s is not supported in this application": "Grant_type: %s nie je podporovaný v tejto aplikácii", "Invalid application or wrong clientSecret": "Neplatná aplikácia alebo nesprávny clientSecret", "Invalid client_id": "Neplatný client_id", "Redirect URI: %s doesn't exist in the allowed Redirect URI list": "Redirect URI: %s neexistuje v zozname povolených Redirect URI", "Token not found, invalid accessToken": "Token nebol n<PERSON>ý, neplatný accessToken"}, "user": {"Display name cannot be empty": "Zobrazované meno nemô<PERSON>e byť prázdne", "MFA email is enabled but email is empty": "MFA e-mail je z<PERSON>, ale e-mail je pr<PERSON>zdny", "MFA phone is enabled but phone number is empty": "MFA telefón je <PERSON>, ale telefónne číslo je prázdne", "New password cannot contain blank space.": "Nové he<PERSON>lo nemôže obsahovať medzery.", "the user's owner and name should not be empty": "vlastník a meno používateľa nesmú byť prázdne"}, "util": {"No application is found for userId: %s": "Nebola nájdená žiadna aplikácia pre userId: %s", "No provider for category: %s is found for application: %s": "Pre aplikáciu: %s nebol nájdený žiadny poskytovateľ pre kategóriu: %s", "The provider: %s is not found": "Poskytovateľ: %s nebol nájdený"}, "verification": {"Invalid captcha provider.": "Neplatný captcha poskytovateľ.", "Phone number is invalid in your region %s": "Telefónne číslo je neplatné vo vašom regióne %s", "The verification code has already been used!": "Overovací kód bol už použitý!", "The verification code has not been sent yet!": "Overovací kód ešte nebol odoslaný!", "Turing test failed.": "Test Turinga zlyhal.", "Unable to get the email modify rule.": "Nepodarilo sa získať pravidlo úpravy e-mailu.", "Unable to get the phone modify rule.": "Nepodarilo sa získať pravidlo úpravy telefónu.", "Unknown type": "Neznámy typ", "Wrong verification code!": "Nesprávny overovací kód!", "You should verify your code in %d min!": "Overte svoj kód za %d minút!", "please add a SMS provider to the \\\"Providers\\\" list for the application: %s": "prosím pridajte SMS poskytovateľa do zoznamu \\\"Poskytovatelia\\\" pre aplikáciu: %s", "please add an Email provider to the \\\"Providers\\\" list for the application: %s": "prosím pridajte e-mailového poskytovateľa do zoznamu \\\"Poskytovatelia\\\" pre aplikáciu: %s", "the user does not exist, please sign up first": "použ<PERSON><PERSON><PERSON> neexistuje, <PERSON>s<PERSON><PERSON>, zaregistrujte sa najskôr"}, "webauthn": {"Found no credentials for this user": "Found no credentials for this user", "Please call WebAuthnSigninBegin first": "Najskôr prosí<PERSON> zavolajte WebAuthnSigninBegin"}}